import { NextApiRequest, NextApiResponse } from 'next';
import { pool, SELECT_TEAMS } from '@/utils/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Pouze GET požadavky
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Získání dat z tabulky game_teams
    const [rows] = await pool.query(SELECT_TEAMS);
    
    // Vrácení dat jako JSON
    return res.status(200).json({ 
      data: rows,
      count: (rows as any[]).length,
      message: 'Data úspěšně získána'
    });
  } catch (error) {
    console.error('Chyba při získávání dat z databáze:', error);
    return res.status(500).json({ 
      message: 'Interní chyba serveru',
      error: String(error)
    });
  }
}
