# Testovací sada pro aplikaci

Tato složka obsahuje kompletní testovací sadu pro aplikaci, kter<PERSON> testuje všechny aspekty aplikace včetně databázových operací, API endpointů, utilit a skriptů.

## Struktura testů

- `database.test.ts`: Testy pro databázové operace (CRUD operace pro týmy, videa a jejich vztahy)
- `database-utils.test.ts`: Testy pro databázové utility funkce exportované z `utils/database.ts`
- `api.test.ts`: Testy pro API endpointy (teams, videos, register, login, unregister, show-data)
- `utils.test.ts`: Testy pro utility funkce (logger, config)
- `scripts.test.ts`: Testy pro skripty (show-database-data)
- `db-test-utils.ts`: Pomocné funkce pro testování databáze

## Spuštění testů

### Všechny testy

```bash
npm test
```

### Testy s průběžným sledováním změn

```bash
npm run test:watch
```

### Testy s pokrytím kódu

```bash
npm run test:coverage
```

### Specifické kategorie testů

```bash
# Databázové testy
npm run test:db

# API testy
npm run test:api

# Utility testy
npm run test:utils

# Testy skriptů
npm run test:scripts
```

## Testovací prostředí

Testy používají stejnou databázi jako produkční aplikace, ale s prefixem `test-` pro testovací data, aby nedošlo k ovlivnění produkčních dat. Můžete nakonfigurovat samostatnou testovací databázi nastavením následujících proměnných prostředí:

- `TEST_DB_HOST`: Hostitel databáze
- `TEST_DB_USER`: Uživatel databáze
- `TEST_DB_PASSWORD`: Heslo databáze
- `TEST_DB_NAME`: Název databáze

Tyto proměnné můžete nastavit v souboru `.env.test`.

## Důležité poznámky

1. Testy vytvářejí a odstraňují testovací data v databázi. Jsou navrženy tak, aby po sobě uklidily, ale pokud test selže, mohou v databázi zůstat některá testovací data.

2. Pro zabránění ovlivnění produkčních dat používají testy prefix `test-` pro všechna testovací data.

3. Testy používají skutečná připojení k databázi, takže vyžadují fungující připojení k databázi.

4. Některé testy mohou selhat, pokud se změní schéma databáze nebo pokud databáze není dostupná.

5. Testy API endpointů používají mock objekty pro HTTP požadavky a odpovědi, takže nevyžadují spuštěný server.

6. Testy pro login API jsou přeskočeny, protože API má problém s čtením dat z těla požadavku při použití metody GET. Toto by mělo být opraveno v implementaci API.

## Pokrytí kódu

Testy jsou navrženy tak, aby poskytovaly vysoké pokrytí kódu. Můžete zobrazit pokrytí kódu spuštěním:

```bash
npm run test:coverage
```

Toto vygeneruje zprávu o pokrytí kódu ve složce `coverage`.
