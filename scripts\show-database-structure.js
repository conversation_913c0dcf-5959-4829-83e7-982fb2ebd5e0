// Skript pro zobrazení struktury databáze
const mysql = require('mysql2/promise');

// Konfigurace databáze
const dbConfig = {
  host: 'camelot.vagonbrei.eu',
  user: 'u6813_cXlIlMkZTS',
  password: '<EMAIL>',
  database: 's6813_web-st',
  connectTimeout: 5000,
};

// Funkce pro zobrazení struktury tabulky
async function showTableStructure(connection, tableName) {
  try {
    console.log(`\n=== Struktura tabulky: ${tableName} ===`);
    
    // Získání struktury tabulky
    const [columns] = await connection.query(`DESCRIBE ${tableName}`);
    
    console.log('Sloupce:');
    columns.forEach(column => {
      console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${column.Key ? `(${column.Key})` : ''} ${column.Default !== null ? `DEFAULT ${column.Default}` : ''} ${column.Extra ? column.Extra : ''}`);
    });
    
    // Získání počtu záznamů
    const [countResult] = await connection.query(`SELECT COUNT(*) as count FROM ${tableName}`);
    console.log(`Počet záznamů: ${countResult[0].count}`);
    
    // Zobrazení prvních 5 záznamů
    if (countResult[0].count > 0) {
      const [rows] = await connection.query(`SELECT * FROM ${tableName} LIMIT 5`);
      console.log('Ukázka dat (prvních 5 záznamů):');
      rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${JSON.stringify(row, null, 2)}`);
      });
    }
    
  } catch (error) {
    console.error(`Chyba při zobrazení struktury tabulky ${tableName}:`, error.message);
  }
}

// Funkce pro zobrazení všech tabulek
async function showAllTables(connection) {
  try {
    console.log('\n=== Seznam všech tabulek ===');
    
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);
    
    console.log('Tabulky v databázi:');
    tableNames.forEach(tableName => {
      console.log(`  - ${tableName}`);
    });
    
    return tableNames;
  } catch (error) {
    console.error('Chyba při získávání seznamu tabulek:', error.message);
    return [];
  }
}

// Hlavní funkce
async function showDatabaseStructure() {
  let connection;
  
  try {
    console.log('Připojuji se k databázi...');
    connection = await mysql.createConnection(dbConfig);
    console.log('Připojení k databázi úspěšné ✓');
    
    // Zobrazení informací o databázi
    console.log(`\n=== Informace o databázi ===`);
    console.log(`Host: ${dbConfig.host}`);
    console.log(`Databáze: ${dbConfig.database}`);
    console.log(`Uživatel: ${dbConfig.user}`);
    
    // Zobrazení všech tabulek
    const tableNames = await showAllTables(connection);
    
    // Zobrazení struktury každé tabulky
    for (const tableName of tableNames) {
      await showTableStructure(connection, tableName);
    }
    
    console.log('\n=== Souhrn ===');
    console.log(`Celkem tabulek: ${tableNames.length}`);
    console.log('Struktura databáze byla úspěšně zobrazena ✓');
    
  } catch (error) {
    console.error('Chyba při připojení k databázi:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\nPřipojení k databázi ukončeno');
    }
  }
}

// Spuštění skriptu
if (require.main === module) {
  showDatabaseStructure();
}

module.exports = { showDatabaseStructure, showTableStructure, showAllTables };
