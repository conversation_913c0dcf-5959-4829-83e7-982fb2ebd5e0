<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Video Tlačítek - Statická verze</title>
    <style>
        :root {
          --primary: #00ff00;
          --primary-dim: #008800;
          --primary-rgb: 0, 255, 0;
          --error: #ff0000;
          --success: #00ff00;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .section-title {
            font-family: 'Fira Code', monospace;
            color: var(--primary);
            margin: 2rem 0 1rem;
            font-size: 1.2rem;
            border-bottom: 1px solid var(--primary-dim);
            padding-bottom: 0.5rem;
        }

        .videos-list-container {
            width: 100%;
            margin-top: 1rem;
        }

        .videos-list {
            width: 100%;
            max-height: 500px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem;
            margin-bottom: 1rem;
        }

        .video-button {
            width: 100%;
            background: linear-gradient(135deg, rgba(20, 20, 20, 0.9), rgba(40, 40, 40, 0.7));
            border: 2px solid var(--primary-dim);
            border-radius: 8px;
            padding: 0;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            overflow: hidden;
            position: relative;
        }

        .video-button:hover {
            border-color: var(--primary);
            background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(60, 60, 60, 0.7));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 0, 0.2);
        }

        .video-button:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0, 255, 0, 0.3);
        }

        .video-button-content {
            display: flex;
            align-items: center;
            padding: 1rem;
            gap: 1rem;
        }

        .video-icon {
            font-size: 2rem;
            filter: grayscale(1);
            transition: filter 0.3s ease;
        }

        .video-button:hover .video-icon {
            filter: grayscale(0);
        }

        .video-details {
            flex: 1;
            text-align: left;
        }

        .video-name {
            font-family: 'Fira Code', monospace;
            color: var(--primary);
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .video-subtitle {
            font-family: 'Fira Code', monospace;
            color: var(--primary-dim);
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .video-play-icon {
            color: var(--primary);
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }

        .video-button:hover .video-play-icon {
            transform: scale(1.2);
            color: #00ff00;
        }

        .videos-count {
            font-family: 'Fira Code', monospace;
            color: var(--primary-dim);
            font-size: 0.9rem;
        }

        .count {
            color: var(--primary);
            font-weight: bold;
        }

        .info {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid var(--primary);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
        }

        .demo-controls {
            margin: 2rem 0;
            padding: 1rem;
            background: rgba(20, 20, 20, 0.7);
            border: 1px solid var(--primary-dim);
        }

        .demo-button {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--primary);
            color: var(--primary);
            padding: 0.5rem 1rem;
            margin: 0.5rem;
            cursor: pointer;
            font-family: 'Fira Code', monospace;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            background: rgba(var(--primary-rgb), 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Test Video Tlačítek - Statická verze</h1>
        
        <div class="info">
            <strong>Tato stránka ukazuje, jak by měla vypadat tlačítka pro videa.</strong><br>
            Tlačítka jsou plně funkční s hover efekty a animacemi.
        </div>

        <div class="demo-controls">
            <strong>Demo ovládání:</strong><br>
            <button class="demo-button" onclick="addVideo()">Přidat video</button>
            <button class="demo-button" onclick="removeVideo()">Odebrat video</button>
            <button class="demo-button" onclick="toggleHover()">Toggle hover efekt</button>
        </div>

        <h2 class="section-title">Odemknutá videa</h2>
        
        <div class="videos-list-container">
            <div class="videos-list" id="videosList">
                <!-- Videa budou přidána JavaScriptem -->
            </div>
            <div class="videos-count">
                Celkem videí: <span class="count" id="videoCount">0</span>
            </div>
        </div>
    </div>

    <script>
        // Testovací data videí
        const testVideos = [
            {
                name: "Video 1",
                code: "A1B2",
                url: "https://www.youtube.com/watch?v=pyGXlTJSTBQ"
            },
            {
                name: "Video 2", 
                code: "C3D4",
                url: "https://www.youtube.com/watch?v=pyGXlTJSTBQ"
            },
            {
                name: "Video 3",
                code: "E5F6", 
                url: "https://www.youtube.com/watch?v=pyGXlTJSTBQ"
            },
            {
                name: "Video 4",
                code: "G7H8",
                url: "https://www.youtube.com/watch?v=pyGXlTJSTBQ"
            }
        ];

        let currentVideos = [];

        function createVideoButton(video) {
            return `
                <button class="video-button" onclick="playVideo('${video.name}', '${video.url}')" type="button">
                    <div class="video-button-content">
                        <div class="video-icon">🎬</div>
                        <div class="video-details">
                            <div class="video-name">${video.name}</div>
                            <div class="video-subtitle">Klikněte pro přehrání</div>
                        </div>
                        <div class="video-play-icon">▶</div>
                    </div>
                </button>
            `;
        }

        function renderVideos() {
            const videosList = document.getElementById('videosList');
            const videoCount = document.getElementById('videoCount');
            
            videosList.innerHTML = currentVideos.map(createVideoButton).join('');
            videoCount.textContent = currentVideos.length;
        }

        function addVideo() {
            if (currentVideos.length < testVideos.length) {
                currentVideos.push(testVideos[currentVideos.length]);
                renderVideos();
            }
        }

        function removeVideo() {
            if (currentVideos.length > 0) {
                currentVideos.pop();
                renderVideos();
            }
        }

        function toggleHover() {
            const buttons = document.querySelectorAll('.video-button');
            buttons.forEach(button => {
                button.classList.toggle('force-hover');
            });
        }

        function playVideo(name, url) {
            alert(`Přehrávám video: ${name}\nURL: ${url}`);
            console.log('Video clicked:', { name, url });
        }

        // Inicializace s prvními dvěma videi
        window.onload = function() {
            currentVideos = testVideos.slice(0, 2);
            renderVideos();
            
            console.log('Test video tlačítek načten');
            console.log('Dostupná videa:', testVideos);
        };
    </script>
</body>
</html>
