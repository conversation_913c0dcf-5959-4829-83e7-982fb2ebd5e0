import { createMocks } from 'node-mocks-http';
import teams<PERSON>andler from '../pages/api/teams';
import videosHandler from '../pages/api/videos';

describe('API Endpoints', () => {
  describe('Teams API', () => {
    test('GET /api/teams should return teams list', async () => {
      const { req, res } = createMocks({
        method: 'GET',
      });

      await teamsHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const data = JSON.parse(res._getData());
      expect(data).toHaveProperty('teams');
      expect(Array.isArray(data.teams)).toBe(true);
    });

    test('Non-GET method should return 405', async () => {
      const { req, res } = createMocks({
        method: 'POST',
      });

      await teamsHandler(req, res);

      expect(res._getStatusCode()).toBe(405);
    });
  });

  describe('Videos API', () => {
    test('GET /api/videos should return videos list', async () => {
      const { req, res } = createMocks({
        method: 'GET',
      });

      await videosHandler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const data = JSON.parse(res._getData());
      expect(data).toHaveProperty('videos');
      expect(Array.isArray(data.videos)).toBe(true);
      expect(data).toHaveProperty('videoDetails');
      expect(Array.isArray(data.videoDetails)).toBe(true);
    });

    test('Non-GET method should return 405', async () => {
      const { req, res } = createMocks({
        method: 'POST',
      });

      await videosHandler(req, res);

      expect(res._getStatusCode()).toBe(405);
    });
  });
});
