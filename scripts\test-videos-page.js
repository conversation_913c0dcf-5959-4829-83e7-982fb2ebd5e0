// Skript pro testování na<PERSON><PERSON> str<PERSON> s videi
const http = require('http');

// Funkce pro testování na<PERSON> stránky videos
function testVideosPage(teamName) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/videos?teamName=${encodeURIComponent(teamName)}`,
      method: 'GET',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Test Script)'
      }
    };

    console.log(`Testuji načítání stránky /videos?teamName=${teamName}...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          html: responseData
        });
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání stránky',
        details: error
      });
    });

    req.end();
  });
}

// Funkce pro analýzu HTML obsahu
function analyzeHTML(html) {
  const analysis = {
    hasVideoButtons: html.includes('video-button'),
    hasVideoIcon: html.includes('🎬'),
    hasPlayIcon: html.includes('▶'),
    hasVideoList: html.includes('videos-list'),
    hasLoadingText: html.includes('Načítání seznamu videí'),
    hasErrorText: html.includes('Chyba při načítání'),
    hasVideoButtonClass: html.includes('class="video-button"'),
    hasVideoButtonContent: html.includes('video-button-content'),
    hasVideoDetails: html.includes('video-details'),
    hasVideoSubtitle: html.includes('Klikněte pro přehrání')
  };

  return analysis;
}

// Hlavní testovací funkce
async function testVideosPageLoad() {
  try {
    console.log('\n=== TEST NAČÍTÁNÍ STRÁNKY S VIDEO TLAČÍTKY ===');
    
    const testTeam = 'Test';
    
    // Test načítání stránky
    console.log('\n--- Test načítání HTML stránky ---');
    try {
      const pageResult = await testVideosPage(testTeam);
      
      if (pageResult.status === 200) {
        console.log('✓ Stránka se úspěšně načetla (status 200)');
        console.log(`✓ Content-Type: ${pageResult.headers['content-type']}`);
        
        // Analýza HTML obsahu
        const analysis = analyzeHTML(pageResult.html);
        
        console.log('\n--- Analýza HTML obsahu ---');
        console.log(`✓ Obsahuje video-button třídu: ${analysis.hasVideoButtonClass ? 'ANO' : 'NE'}`);
        console.log(`✓ Obsahuje video-button-content: ${analysis.hasVideoButtonContent ? 'ANO' : 'NE'}`);
        console.log(`✓ Obsahuje video-details: ${analysis.hasVideoDetails ? 'ANO' : 'NE'}`);
        console.log(`✓ Obsahuje ikonu 🎬: ${analysis.hasVideoIcon ? 'ANO' : 'NE'}`);
        console.log(`✓ Obsahuje play ikonu ▶: ${analysis.hasPlayIcon ? 'ANO' : 'NE'}`);
        console.log(`✓ Obsahuje videos-list: ${analysis.hasVideoList ? 'ANO' : 'NE'}`);
        console.log(`✓ Obsahuje subtitle "Klikněte pro přehrání": ${analysis.hasVideoSubtitle ? 'ANO' : 'NE'}`);
        console.log(`⚠ Obsahuje loading text: ${analysis.hasLoadingText ? 'ANO' : 'NE'}`);
        console.log(`⚠ Obsahuje error text: ${analysis.hasErrorText ? 'ANO' : 'NE'}`);
        
        // Hledání specifických částí HTML
        console.log('\n--- Hledání klíčových elementů ---');
        
        // Hledání button elementů
        const buttonMatches = pageResult.html.match(/<button[^>]*class="video-button"[^>]*>/g);
        if (buttonMatches) {
          console.log(`✓ Nalezeno ${buttonMatches.length} video tlačítek`);
        } else {
          console.log('⚠ Nenalezena žádná video tlačítka');
        }
        
        // Hledání video názvů
        const videoNameMatches = pageResult.html.match(/class="video-name"[^>]*>([^<]+)</g);
        if (videoNameMatches) {
          console.log(`✓ Nalezeny video názvy: ${videoNameMatches.length}`);
          videoNameMatches.forEach((match, index) => {
            const name = match.replace(/class="video-name"[^>]*>/, '');
            console.log(`  ${index + 1}. ${name}`);
          });
        }
        
        // Kontrola CSS stylů
        const hasVideoButtonStyles = pageResult.html.includes('.video-button {');
        const hasVideoButtonContentStyles = pageResult.html.includes('.video-button-content {');
        const hasVideoIconStyles = pageResult.html.includes('.video-icon {');
        
        console.log('\n--- CSS styly ---');
        console.log(`✓ Video button styly: ${hasVideoButtonStyles ? 'PŘÍTOMNY' : 'CHYBÍ'}`);
        console.log(`✓ Video button content styly: ${hasVideoButtonContentStyles ? 'PŘÍTOMNY' : 'CHYBÍ'}`);
        console.log(`✓ Video icon styly: ${hasVideoIconStyles ? 'PŘÍTOMNY' : 'CHYBÍ'}`);
        
        // Ukázka části HTML s tlačítky
        console.log('\n--- Ukázka HTML (první 500 znaků s tlačítky) ---');
        const buttonIndex = pageResult.html.indexOf('video-button');
        if (buttonIndex !== -1) {
          const excerpt = pageResult.html.substring(buttonIndex - 100, buttonIndex + 400);
          console.log(excerpt.replace(/\n\s*/g, ' ').trim());
        } else {
          console.log('Nenalezeny žádné video-button elementy v HTML');
        }
        
      } else {
        console.log(`✗ Stránka vrátila status ${pageResult.status}`);
      }
      
    } catch (error) {
      console.error('✗ Chyba při načítání stránky:', error);
    }

    console.log('\n=== SHRNUTÍ ===');
    console.log('✓ Test načítání stránky dokončen');
    console.log('🎯 Zkontrolujte výsledky výše pro ověření tlačítek');
    console.log('🌐 Otevřete http://localhost:3000/videos?teamName=Test v prohlížeči');

  } catch (error) {
    console.error('Chyba při testování:', error);
  }
}

testVideosPageLoad();
