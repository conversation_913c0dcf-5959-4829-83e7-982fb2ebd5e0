// Skript pro testování API pro ověření kódu
const http = require('http');

// Funkce pro testování ověření kódu
function testVerifyCode(code) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      code: code
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/verify-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    console.log(`Zkouším ověřit kód: "${code}"...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            result: result
          });
        } catch (error) {
          reject({
            error: 'Chyba při zpracování odpovědi',
            details: error,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání API',
        details: error
      });
    });

    req.write(data);
    req.end();
  });
}

// Testování platného kódu
async function runTests() {
  try {
    // Test platných kódů
    console.log('\n=== PLATNÉ KÓDY ===');
    const validCodes = ['LrFYwPr9ciOm07PB', 'ukIskibidinn7Xr5o', 'qp3pw4xweKM2wa', 'iFGQ0tn3pRqJ', 'gK7twglt0hIh7DoQ'];
    for (const code of validCodes) {
      try {
        const result = await testVerifyCode(code);
        console.log(`Kód "${code}": ${result.status === 200 ? 'PLATNÝ ✓' : 'NEPLATNÝ ✗'}`);
        console.log('Výsledek:', result.result);
      } catch (error) {
        console.error('Chyba při testování:', error);
      }
    }

    // Test neplatných kódů
    console.log('\n=== NEPLATNÉ KÓDY ===');
    const invalidCodes = ['neexistujici_kod', '123456', 'test', ''];
    for (const code of invalidCodes) {
      try {
        const result = await testVerifyCode(code);
        console.log(`Kód "${code}": ${result.status === 200 ? 'PLATNÝ ✓' : 'NEPLATNÝ ✗'}`);
        console.log('Výsledek:', result.result);
      } catch (error) {
        console.error('Chyba při testování:', error);
      }
    }
  } catch (error) {
    console.error('Chyba při testování:', error);
  }
}

runTests();
