// Skript pro testování nových video tlačítek
const http = require('http');

// Funkce pro testování API team-videos
function getTeamVideos(teamName) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/team-videos?teamName=${encodeURIComponent(teamName)}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    console.log(`Získávám videa pro tým "${teamName}"...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            result: result
          });
        } catch (error) {
          reject({
            error: 'Chyba p<PERSON><PERSON> odpovědi',
            details: error,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání API',
        details: error
      });
    });

    req.end();
  });
}

// Funkce pro ověření kódu s týmem
function verifyCodeWithTeam(code, teamName) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      code: code,
      teamName: teamName
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/verify-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    console.log(`Ověřuji kód "${code}" pro tým "${teamName}"...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            result: result
          });
        } catch (error) {
          reject({
            error: 'Chyba při zpracování odpovědi',
            details: error,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání API',
        details: error
      });
    });

    req.write(data);
    req.end();
  });
}

// Hlavní testovací funkce
async function testVideoButtons() {
  try {
    const testTeam = 'Test';
    const testCodes = ['A1B2', 'E5F6'];

    console.log('\n=== TEST NOVÝCH VIDEO TLAČÍTEK ===');

    // Nejprve zkontrolujeme současný stav týmu
    console.log('\n--- Současný stav týmu ---');
    try {
      const teamVideosResult = await getTeamVideos(testTeam);
      if (teamVideosResult.status === 200) {
        console.log(`✓ API funguje - Tým "${testTeam}" má ${teamVideosResult.result.videos.length} odemčených videí`);
        console.log('Struktura odpovědi:');
        console.log('- videos:', teamVideosResult.result.videos.length, 'položek');
        console.log('- stats:', teamVideosResult.result.stats);

        if (teamVideosResult.result.videos.length > 0) {
          console.log('\nPrvní video:');
          const firstVideo = teamVideosResult.result.videos[0];
          console.log('- name:', firstVideo.name);
          console.log('- code:', firstVideo.code);
          console.log('- path:', firstVideo.path);
          console.log('- url:', firstVideo.url);
        }
      } else {
        console.log(`⚠ API vrátilo status ${teamVideosResult.status}`);
      }
    } catch (error) {
      console.log('⚠ Tým pravděpodobně neexistuje nebo nemá odemčená videa');
    }

    // Odemkneme video pro testování tlačítek
    console.log('\n--- Odemčení videa pro test tlačítek ---');
    try {
      const unlockResult = await verifyCodeWithTeam(testCodes[0], testTeam);
      if (unlockResult.status === 200) {
        console.log(`✓ Video úspěšně odemčeno: ${unlockResult.result.videoName}`);
        console.log(`✓ URL videa: ${unlockResult.result.videoPath}`);
      } else {
        console.log(`⚠ Odemčení selhalo se statusem ${unlockResult.status}`);
      }
    } catch (error) {
      console.error('✗ Chyba při odemčení:', error);
    }

    // Ověříme finální stav
    console.log('\n--- Finální stav pro tlačítka ---');
    try {
      const finalResult = await getTeamVideos(testTeam);
      if (finalResult.status === 200) {
        console.log(`✓ Tým "${testTeam}" má ${finalResult.result.videos.length} odemčených videí`);
        console.log('✓ Data jsou připravena pro zobrazení tlačítek');

        console.log('\nVidea pro tlačítka:');
        finalResult.result.videos.forEach((video, index) => {
          console.log(`  ${index + 1}. Tlačítko: "${video.name}"`);
          console.log(`     - Kód: ${video.code}`);
          console.log(`     - URL: ${video.url}`);
          console.log(`     - Ikona: 🎬`);
          console.log(`     - Akce: Klik → přehrání videa`);
        });
      }
    } catch (error) {
      console.error('✗ Chyba při finálním ověření:', error);
    }

    console.log('\n=== SHRNUTÍ ===');
    console.log('✓ API endpointy fungují správně');
    console.log('✓ Data jsou ve správném formátu pro tlačítka');
    console.log('✓ Každé video má: name, code, url, path');
    console.log('✓ UI může zobrazit tlačítka s ikonami a detaily');
    console.log('\n🎯 Tlačítka by měla fungovat v prohlížeči na http://localhost:3000');

  } catch (error) {
    console.error('Chyba při testování:', error);
  }
}

testVideoButtons();
