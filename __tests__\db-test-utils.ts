import mysql from 'mysql2/promise';

// Database configuration for tests
const dbConfig = {
  host: 'camelot.vagonbrei.eu',
  user: 'u6813_cXlIlMkZTS',
  password: '<EMAIL>',
  database: 's6813_web-st',
  connectTimeout: 5000,
};

// Create a test database connection pool with retry logic
export const createTestPool = async (maxRetries = 3, retryDelay = 1000): Promise<mysql.Pool> => {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const pool = mysql.createPool(dbConfig);

      // Test the connection
      await pool.query('SELECT 1');
      console.log('Database connection established successfully');
      return pool;
    } catch (error) {
      retries++;
      console.log(`Database connection attempt ${retries} failed: ${error}`);

      if (retries >= maxRetries) {
        console.error('Max retries reached. Could not connect to database.');
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  // This should never be reached due to the throw in the catch block
  throw new Error('Failed to connect to database after maximum retries');
};

// Helper function to safely execute a database query with retries
export const safeQuery = async (
  pool: mysql.Pool,
  query: string,
  params: any[] = [],
  maxRetries = 3,
  retryDelay = 1000
): Promise<[any[], mysql.FieldPacket[]]> => {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      return await pool.query(query, params);
    } catch (error) {
      retries++;
      console.log(`Query attempt ${retries} failed: ${error}`);

      if (retries >= maxRetries) {
        console.error('Max retries reached. Query failed.');
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  // This should never be reached due to the throw in the catch block
  throw new Error('Failed to execute query after maximum retries');
};

// Helper function to safely close a database connection
export const safeEnd = async (pool: mysql.Pool): Promise<void> => {
  try {
    await pool.end();
    console.log('Database connection closed successfully');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
};

// Helper function to create test tables if they don't exist
export const setupTestTables = async (pool: mysql.Pool): Promise<void> => {
  try {
    // Create videos table if it doesn't exist
    await safeQuery(
      pool,
      `CREATE TABLE IF NOT EXISTS game_videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        file_path VARCHAR(255) NOT NULL,
        content_type VARCHAR(50) NOT NULL,
        size INT NOT NULL,
        code VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    );

    // Create teams table if it doesn't exist
    await safeQuery(
      pool,
      `CREATE TABLE IF NOT EXISTS game_teams (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        status INT DEFAULT 0,
        time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    );

    // Create team_videos table if it doesn't exist
    await safeQuery(
      pool,
      `CREATE TABLE IF NOT EXISTS team_videos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        team_name VARCHAR(255) NOT NULL,
        video_name VARCHAR(255) NOT NULL,
        unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY team_video_unique (team_name, video_name)
      )`
    );

    console.log('Test tables created successfully');
  } catch (error) {
    console.error('Error creating test tables:', error);
    throw error;
  }
};

// Helper function to insert test data
export const insertTestData = async (pool: mysql.Pool): Promise<void> => {
  try {
    // Insert test videos
    await safeQuery(
      pool,
      'INSERT IGNORE INTO game_videos (name, file_path, content_type, size, code) VALUES (?, ?, ?, ?, ?)',
      ['test-video-1', '/videos/test-video-1.mp4', 'video/mp4', 1024, 'TEST1']
    );

    await safeQuery(
      pool,
      'INSERT IGNORE INTO game_videos (name, file_path, content_type, size, code) VALUES (?, ?, ?, ?, ?)',
      ['test-video-2', '/videos/test-video-2.mp4', 'video/mp4', 2048, 'TEST2']
    );

    // Insert test teams
    await safeQuery(
      pool,
      'INSERT IGNORE INTO game_teams (name, password, status) VALUES (?, ?, ?)',
      ['test-team-1', 'password1', 0]
    );

    await safeQuery(
      pool,
      'INSERT IGNORE INTO game_teams (name, password, status) VALUES (?, ?, ?)',
      ['test-team-2', 'password2', 1]
    );

    // Insert test team-video relations
    await safeQuery(
      pool,
      'INSERT IGNORE INTO team_videos (team_name, video_name) VALUES (?, ?)',
      ['test-team-1', 'test-video-1']
    );

    console.log('Test data inserted successfully');
  } catch (error) {
    console.error('Error inserting test data:', error);
    throw error;
  }
};

// Helper function to clean up test data
export const cleanupTestData = async (pool: mysql.Pool): Promise<void> => {
  try {
    // Delete test team-video relations
    await safeQuery(
      pool,
      'DELETE FROM team_videos WHERE team_name LIKE ? OR video_name LIKE ?',
      ['test-%', 'test-%']
    );

    // Delete test teams
    await safeQuery(
      pool,
      'DELETE FROM game_teams WHERE name LIKE ?',
      ['test-%']
    );

    // Delete test videos
    await safeQuery(
      pool,
      'DELETE FROM game_videos WHERE name LIKE ?',
      ['test-%']
    );

    console.log('Test data cleaned up successfully');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
    throw error;
  }
};
