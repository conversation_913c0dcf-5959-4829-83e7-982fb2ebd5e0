// Skript pro testování odemčení videa pro tým
const http = require('http');

// Funkce pro testování ověř<PERSON><PERSON> kódu s týmem
function testVerifyCodeWithTeam(code, teamName) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      code: code,
      teamName: teamName
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/verify-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    console.log(`Zkouším odemknout video kódem "${code}" pro tým "${teamName}"...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            result: result
          });
        } catch (error) {
          reject({
            error: 'Chyba při z<PERSON> odpovědi',
            details: error,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání API',
        details: error
      });
    });

    req.write(data);
    req.end();
  });
}

// Funkce pro získání videí týmu
function getTeamVideos(teamName) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/team-videos?teamName=${encodeURIComponent(teamName)}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    console.log(`Získávám videa pro tým "${teamName}"...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            result: result
          });
        } catch (error) {
          reject({
            error: 'Chyba při zpracování odpovědi',
            details: error,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání API',
        details: error
      });
    });

    req.end();
  });
}

// Hlavní testovací funkce
async function runTeamUnlockTest() {
  try {
    const testTeam = 'Test';
    const testCodes = ['A1B2', 'C3D4'];

    console.log('\n=== TEST ODEMČENÍ VIDEÍ PRO TÝM ===');
    
    // Nejprve zkontrolujeme současný stav týmu
    console.log('\n--- Současný stav týmu ---');
    try {
      const teamVideosResult = await getTeamVideos(testTeam);
      console.log(`Tým "${testTeam}" má ${teamVideosResult.result.videos.length} odemčených videí`);
      console.log('Statistiky:', teamVideosResult.result.stats);
    } catch (error) {
      console.error('Chyba při získávání videí týmu:', error);
    }

    // Odemkneme první video
    console.log('\n--- Odemčení prvního videa ---');
    try {
      const unlockResult1 = await testVerifyCodeWithTeam(testCodes[0], testTeam);
      console.log(`Odemčení kódem "${testCodes[0]}": ${unlockResult1.status === 200 ? 'ÚSPĚŠNÉ ✓' : 'NEÚSPĚŠNÉ ✗'}`);
      console.log('Výsledek:', JSON.stringify(unlockResult1.result, null, 2));
    } catch (error) {
      console.error('Chyba při odemčení:', error);
    }

    // Zkontrolujeme stav po prvním odemčení
    console.log('\n--- Stav po prvním odemčení ---');
    try {
      const teamVideosResult = await getTeamVideos(testTeam);
      console.log(`Tým "${testTeam}" má ${teamVideosResult.result.videos.length} odemčených videí`);
      console.log('Statistiky:', teamVideosResult.result.stats);
      console.log('Odemčená videa:');
      teamVideosResult.result.videos.forEach((video, index) => {
        console.log(`  ${index + 1}. ${video.name} (kód: ${video.code}) - ${video.url}`);
      });
    } catch (error) {
      console.error('Chyba při získávání videí týmu:', error);
    }

    // Odemkneme druhé video
    console.log('\n--- Odemčení druhého videa ---');
    try {
      const unlockResult2 = await testVerifyCodeWithTeam(testCodes[1], testTeam);
      console.log(`Odemčení kódem "${testCodes[1]}": ${unlockResult2.status === 200 ? 'ÚSPĚŠNÉ ✓' : 'NEÚSPĚŠNÉ ✗'}`);
      console.log('Výsledek:', JSON.stringify(unlockResult2.result, null, 2));
    } catch (error) {
      console.error('Chyba při odemčení:', error);
    }

    // Finální stav
    console.log('\n--- Finální stav ---');
    try {
      const teamVideosResult = await getTeamVideos(testTeam);
      console.log(`Tým "${testTeam}" má ${teamVideosResult.result.videos.length} odemčených videí`);
      console.log('Statistiky:', teamVideosResult.result.stats);
      console.log('Odemčená videa:');
      teamVideosResult.result.videos.forEach((video, index) => {
        console.log(`  ${index + 1}. ${video.name} (kód: ${video.code}) - ${video.url}`);
      });
    } catch (error) {
      console.error('Chyba při získávání videí týmu:', error);
    }

    console.log('\n=== TEST DOKONČEN ===');

  } catch (error) {
    console.error('Chyba při testování:', error);
  }
}

runTeamUnlockTest();
