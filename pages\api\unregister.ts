import { DELETE_TEAM, pool } from "@/utils/database";
import { error, log } from "@/utils/logger";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'DELETE') {
        return res.status(405).send(error("This method isn't supported!"))
    }

    const { name } = req.body;
    if (name == null) {
        return res.status(404).send(error("Invalid data!"));
    }

    deleteTeam(name).then(() => {
        return res.status(200).send(log("Ok"));
    }).catch(() => {
        return res.status(400).send(log("The query has failed!"));
    });

}

async function deleteTeam(name: string): Promise<void> {
    await pool.query(DELETE_TEAM, [name]);
}

