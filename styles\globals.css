/* Optimalizovaný import fontů - na<PERSON><PERSON><PERSON><PERSON> pouze potřebn<PERSON> v<PERSON>hy */
@import url('https://fonts.googleapis.com/css2?family=VT323&family=Fira+Code:wght@400;500&display=swap');

:root {
    --primary: #0f0;
    --primary-dim: #0f03;
    --secondary: #f00;
    --dark: #000000;
    --light: #00ff00;
    --success: #0f0;
    --error: #f00;
    --card-bg: rgba(0, 0, 0, 0.85);
    --card-border: rgba(0, 255, 0, 0.2);
    --terminal-green: #0f0;
    --terminal-red: #f00;
    --glitch-blue: #0ff;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    cursor: none;
}

html, body {
    overflow-x: hidden;
    width: 100%;
    height: 100%;
}

body {
    font-family: 'Fira Code', monospace;
    background-color: var(--dark);
    color: var(--light);
    position: relative;
}

/* Custom cursor */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    border: 1px solid var(--primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    /* Odstraněn transition pro okamžitou reakci */
    will-change: transform;
}

.cursor::after {
    content: '';
    position: absolute;
    width: 5px;
    height: 5px;
    background: var(--primary);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.cursor.active {
    /* Změna velikosti bez použití transform pro okamžitou reakci */
    width: 14px;
    height: 14px;
}

/* Background */
html {
    background-color: #000;
    height: 100svh;
    position: relative;
}

html::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            0deg,
            rgba(0, 0, 0, 0.15),
            rgba(0, 0, 0, 0.15) 1px,
            transparent 1px,
            transparent 2px
        );
    pointer-events: none;
    z-index: 1;
}

/* Matrix rain effect */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
}

.matrix-column {
    position: absolute;
    top: -100%;
    width: 20px;
    color: rgba(0, 255, 0, 0.5);
    font-family: 'VT323', monospace;
    font-size: 1.2rem;
    text-align: center;
    animation: matrix-rain linear infinite;
    /* Přidáno pro optimalizaci výkonu */
    will-change: transform, opacity;
    transform: translateZ(0);
}

@keyframes matrix-rain {
    0% { top: -100%; }
    100% { top: 100%; }
}

/* Glitch effect */
@keyframes glitch {
    0% {
        transform: translate(0);
    }
    20% {
        transform: translate(-2px, 2px);
    }
    40% {
        transform: translate(-2px, -2px);
    }
    60% {
        transform: translate(2px, 2px);
    }
    80% {
        transform: translate(2px, -2px);
    }
    100% {
        transform: translate(0);
    }
}

@keyframes glitch-2 {
    0% {
        transform: translate(0);
    }
    20% {
        transform: translate(3px, -3px);
    }
    40% {
        transform: translate(3px, 3px);
    }
    60% {
        transform: translate(-3px, -3px);
    }
    80% {
        transform: translate(-3px, 3px);
    }
    100% {
        transform: translate(0);
    }
}

.glitch {
    position: relative;
    animation: glitch 1s infinite;
    /* Přidáno pro optimalizaci výkonu */
    will-change: transform;
    transform: translateZ(0);
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    /* Přidáno pro optimalizaci výkonu */
    will-change: transform, clip;
}

.glitch::before {
    left: 2px;
    text-shadow: -1px 0 var(--glitch-blue);
    clip: rect(44px, 450px, 56px, 0);
    animation: glitch-2 5s infinite linear alternate-reverse;
}

.glitch::after {
    left: -2px;
    text-shadow: -1px 0 var(--secondary);
    clip: rect(44px, 450px, 56px, 0);
    animation: glitch-2 5s infinite linear alternate-reverse;
}

main {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.base {
    max-width: 450px;
    width: 100%;
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 4px;
    padding: 2rem;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.2);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.terminal-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--primary-dim);
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    font-family: 'VT323', monospace;
}

.terminal-title {
    color: var(--primary);
    font-size: 1rem;
}

.terminal-controls {
    display: flex;
    gap: 0.5rem;
}

.terminal-control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.terminal-control.close {
    background-color: var(--terminal-red);
}

.terminal-control.minimize {
    background-color: #ff0;
}

.terminal-control.maximize {
    background-color: var(--terminal-green);
}

.logo {
    font-family: 'VT323', monospace;
    font-size: 2.5rem;
    color: var(--primary);
    text-shadow: 0 0 5px var(--primary);
    margin-bottom: 1rem;
    letter-spacing: 2px;
    text-transform: uppercase;
    position: relative;
}

.form-group {
    position: relative;
    width: 100%;
    margin-bottom: 1.5rem;
}

.form-group::before {
    content: '>';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary);
    font-family: 'VT323', monospace;
    z-index: 1;
}

input {
    width: 100%;
    padding: 0.8rem 0.8rem 0.8rem 2rem;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--primary-dim);
    font-family: 'Fira Code', monospace;
    font-size: 1rem;
    color: var(--light);
    transition: all 0.3s ease;
    caret-color: var(--primary);
}

input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

input::placeholder {
    color: rgba(0, 255, 0, 0.5);
    font-family: 'Fira Code', monospace;
}

.error-message {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 0, 0, 0.2);
    border: 1px solid var(--error);
    color: var(--error);
    text-align: center;
    padding: 1rem;
    font-family: 'Fira Code', monospace;
    font-weight: 500;
    transform: translateY(-100%);
    animation: slideDown 0.5s forwards, glitch 0.2s infinite;
    z-index: 10;
}

@keyframes slideDown {
    to { transform: translateY(0); }
}

.success-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 1.5rem;
}

.success-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--success);
    font-family: 'VT323', monospace;
    text-shadow: 0 0 5px var(--success);
}

.success-message {
    color: var(--primary);
    margin-bottom: 2rem;
    font-family: 'Fira Code', monospace;
}

/* Submit button styles */
.submit-button {
    background: rgba(0, 0, 0, 0.7);
    color: var(--primary);
    border: 1px solid var(--primary-dim);
    border-radius: 0;
    padding: 0.8rem 1.5rem;
    font-family: 'Fira Code', monospace;
    font-size: 1rem;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.submit-button:hover:not(:disabled) {
    background: rgba(0, 255, 0, 0.1);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
    text-shadow: 0 0 5px var(--primary);
}

.submit-button:active:not(:disabled) {
    background: rgba(0, 255, 0, 0.2);
}

.submit-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: rgba(0, 255, 0, 0.1);
    color: rgba(0, 255, 0, 0.3);
}

.submit-button.loading {
    position: relative;
}

.submit-button.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 255, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
    right: 15px;
    top: calc(50% - 8px);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Typing animation */
.typing {
    border-right: 2px solid var(--primary);
    white-space: nowrap;
    overflow: hidden;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: var(--primary) }
}

/* Video styles */
.video-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    background: #000;
    overflow: hidden;
}

.fullscreen-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
}

/* Teams page styles */
.teams-page {
    max-width: 600px;
}

.teams-list-container {
    width: 100%;
    margin-top: 1rem;
}

.teams-list {
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--primary-dim);
    background: rgba(0, 0, 0, 0.3);
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.team-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    border-bottom: 1px solid var(--primary-dim);
    font-family: 'Fira Code', monospace;
}

.team-item:last-child {
    border-bottom: none;
}

.team-name {
    color: var(--primary);
    font-weight: 500;
}

.team-date {
    color: var(--primary-dim);
    font-size: 0.8rem;
}

.teams-count {
    text-align: right;
    font-size: 0.9rem;
    color: var(--primary-dim);
    font-family: 'Fira Code', monospace;
}

/* Přístupnost */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Responzivní design */
@media (max-width: 768px) {
    .base {
        max-width: 100%;
        padding: 1.5rem;
    }

    .logo {
        font-size: 2rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    input {
        padding: 0.6rem 0.6rem 0.6rem 2rem;
        font-size: 0.9rem;
    }

    .submit-button {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .teams-list {
        max-height: 250px;
    }
}

@media (max-width: 480px) {
    main {
        padding: 1rem;
    }

    .base {
        padding: 1rem;
    }

    .logo {
        font-size: 1.8rem;
    }

    .form-group::before {
        left: 8px;
        font-size: 0.9rem;
    }

    input {
        padding: 0.5rem 0.5rem 0.5rem 1.8rem;
        font-size: 0.8rem;
    }

    .submit-button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .teams-list {
        max-height: 200px;
    }

    .team-item {
        padding: 0.4rem;
        font-size: 0.8rem;
    }

    .team-date {
        font-size: 0.7rem;
    }
}

.count {
    color: var(--primary);
    font-weight: 500;
}

.loading-message, .no-teams-message {
    color: var(--primary-dim);
    text-align: center;
    padding: 2rem 0;
    font-family: 'Fira Code', monospace;
}

.back-link {
    margin-top: 1.5rem;
    color: var(--primary-dim);
    text-decoration: none;
    font-size: 0.9rem;
    border: 1px solid var(--primary-dim);
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    font-family: 'Fira Code', monospace;
}

.back-link:hover {
    color: var(--primary);
    border-color: var(--primary);
    background: rgba(0, 255, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .base {
        width: 90%;
        padding: 1.5rem 1rem;
    }

    .logo {
        font-size: 2rem;
    }

    .submit-button {
        padding: 0.7rem 1.2rem;
    }

    .teams-list {
        max-height: 250px;
    }

    .team-item {
        flex-direction: column;
    }

    .team-date {
        margin-top: 0.3rem;
    }
}