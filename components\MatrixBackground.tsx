import React, { useEffect, memo } from 'react';

const MatrixBackground: React.FC = () => {
  useEffect(() => {
    const matrixBg = document.querySelector('.matrix-bg');
    if (!matrixBg) return;
    
    // Clear any existing columns
    matrixBg.innerHTML = '';
    
    const characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789';
    
    // Optimalizace: Snížení počtu sloupců pro lepší výkon
    const columnWidth = 20;
    const columns = Math.min(50, Math.floor(window.innerWidth / columnWidth));
    
    for (let i = 0; i < columns; i++) {
      const column = document.createElement('div');
      column.className = 'matrix-column';
      column.style.left = `${i * (window.innerWidth / columns)}px`;
      column.style.animationDuration = `${Math.random() * 10 + 10}s`;
      
      // Optimalizace: Snížení počtu znaků v každém sloupci
      const columnHeight = Math.min(30, Math.floor(Math.random() * 20) + 10);
      
      for (let j = 0; j < columnHeight; j++) {
        const char = document.createElement('div');
        char.textContent = characters.charAt(Math.floor(Math.random() * characters.length));
        char.style.opacity = j === 0 ? '1' : `${Math.random() * 0.5 + 0.1}`;
        column.appendChild(char);
      }
      
      matrixBg.appendChild(column);
    }
    
    // Cleanup function
    return () => {
      if (matrixBg) {
        matrixBg.innerHTML = '';
      }
    };
  }, []);

  return <div className="matrix-bg"></div>;
};

export default memo(MatrixBackground);
