<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test YouTube Embed URLs</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }
        .iframe-container {
            margin: 1rem 0;
            border: 2px solid #00ff00;
        }
        iframe {
            width: 100%;
            height: 315px;
            border: none;
        }
        .url-display {
            font-family: monospace;
            background: rgba(0, 0, 0, 0.5);
            padding: 0.5rem;
            margin: 0.5rem 0;
            word-break: break-all;
        }
        .status {
            padding: 0.5rem;
            margin: 0.5rem 0;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .warning { color: #ffff00; }
        .button {
            background: #001100;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            font-family: inherit;
        }
        .button:hover {
            background: #002200;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Test YouTube Embed URLs</h1>
        
        <div class="test-section">
            <h2>Test 1: Embed URL z databáze</h2>
            <div class="url-display">https://www.youtube.com/embed/pyGXlTJSTBQ</div>
            <div class="iframe-container">
                <iframe 
                    src="https://www.youtube.com/embed/pyGXlTJSTBQ" 
                    title="YouTube video player" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
                </iframe>
            </div>
            <div class="status success">✓ Toto by mělo fungovat - embed URL</div>
        </div>

        <div class="test-section">
            <h2>Test 2: Běžná YouTube URL (nefunguje v iframe)</h2>
            <div class="url-display">https://www.youtube.com/watch?v=pyGXlTJSTBQ</div>
            <div class="iframe-container">
                <iframe 
                    src="https://www.youtube.com/watch?v=pyGXlTJSTBQ" 
                    title="YouTube video player" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
                </iframe>
            </div>
            <div class="status error">✗ Toto nefunguje - běžná URL</div>
        </div>

        <div class="test-section">
            <h2>Test 3: Konverze URL</h2>
            <p>Testování JavaScript funkce pro konverzi URL:</p>
            <input type="text" id="urlInput" placeholder="Vložte YouTube URL" style="width: 70%; padding: 5px; background: #000; color: #00ff00; border: 1px solid #00ff00;">
            <button class="button" onclick="convertUrl()">Konvertovat</button>
            <div id="conversionResult" class="url-display" style="margin-top: 10px;"></div>
            <div id="testIframe" class="iframe-container" style="display: none;">
                <iframe id="testVideo" title="Test video" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
        </div>

        <div class="test-section">
            <h2>Test 4: API Response</h2>
            <button class="button" onclick="testAPI()">Test API team-videos</button>
            <div id="apiResult"></div>
        </div>

        <div class="test-section">
            <h2>Návod pro opravu</h2>
            <div class="status warning">
                <strong>Pokud vidíte "refused to connect":</strong><br>
                1. URL musí být ve formátu: https://www.youtube.com/embed/VIDEO_ID<br>
                2. Nikoli: https://www.youtube.com/watch?v=VIDEO_ID<br>
                3. Databáze byla aktualizována s embed URL<br>
                4. JavaScript funkce automaticky konvertuje URL
            </div>
        </div>
    </div>

    <script>
        // Funkce pro konverzi YouTube URL na embed formát
        function convertToEmbedUrl(url) {
            // Pokud už je embed URL, vrátíme jak je
            if (url.includes('/embed/')) {
                return url;
            }
            
            // Konvertujeme watch URL na embed URL
            const videoIdMatch = url.match(/[?&]v=([^&]+)/);
            if (videoIdMatch) {
                const videoId = videoIdMatch[1];
                return `https://www.youtube.com/embed/${videoId}`;
            }
            
            // Vrátíme původní URL pokud není potřeba konverze
            return url;
        }

        function convertUrl() {
            const input = document.getElementById('urlInput');
            const result = document.getElementById('conversionResult');
            const iframeContainer = document.getElementById('testIframe');
            const iframe = document.getElementById('testVideo');
            
            const originalUrl = input.value.trim();
            if (!originalUrl) {
                result.textContent = 'Zadejte URL adresu';
                return;
            }
            
            const embedUrl = convertToEmbedUrl(originalUrl);
            result.innerHTML = `
                <strong>Původní:</strong> ${originalUrl}<br>
                <strong>Konvertovaná:</strong> ${embedUrl}
            `;
            
            // Zobrazíme test iframe
            iframe.src = embedUrl;
            iframeContainer.style.display = 'block';
        }

        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="status">Načítám data z API...</div>';
            
            try {
                const response = await fetch('/api/team-videos?teamName=Test');
                const data = await response.json();
                
                if (response.ok && data.videos) {
                    let html = '<div class="status success">✓ API funguje</div>';
                    html += `<div><strong>Počet videí:</strong> ${data.videos.length}</div>`;
                    
                    data.videos.forEach((video, index) => {
                        const isEmbed = video.url.includes('/embed/');
                        const statusClass = isEmbed ? 'success' : 'error';
                        const statusText = isEmbed ? '✓ Embed URL' : '✗ Běžná URL';
                        
                        html += `
                            <div style="margin: 1rem 0; padding: 0.5rem; border: 1px solid #444;">
                                <strong>${video.name}</strong> (${video.code})<br>
                                <div class="url-display">${video.url}</div>
                                <div class="status ${statusClass}">${statusText}</div>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = '<div class="status error">✗ API chyba: ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="status error">✗ Chyba: ' + error.message + '</div>';
            }
        }

        // Automaticky otestujeme API při načtení
        window.onload = function() {
            // Předvyplníme testovací URL
            document.getElementById('urlInput').value = 'https://www.youtube.com/watch?v=pyGXlTJSTBQ';
            
            // Otestujeme API
            setTimeout(testAPI, 1000);
        };
    </script>
</body>
</html>
