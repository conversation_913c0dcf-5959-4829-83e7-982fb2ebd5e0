// Set timeout for tests to 30 seconds
jest.setTimeout(30000);

// Add custom matchers if needed
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },
});

// Global setup
beforeAll(() => {
  console.log('Starting test suite');
});

// Global teardown
afterAll(() => {
  console.log('Test suite completed');
});
