import mysql from 'mysql2/promise';

export const pool = mysql.createPool({
  host: 'camelot.vagonbrei.eu',
  user: 'u6813_cXlIlMkZTS',
  password: '<EMAIL>',
  database: 's6813_web-st',
  connectTimeout: 5000,
});

// Team queries
export const INSERT_TEAM = 'INSERT INTO game_teams(`name`, `password`, `status`, `time`) VALUES (?, ?, ?, ?)';
export const DELETE_TEAM = 'DELETE FROM game_teams WHERE name = ?';
export const SELECT_TEAMS = 'SELECT * FROM game_teams';
export const SELECT_TEAM_BY_NAME = 'SELECT * FROM game_teams WHERE name = ?';

// Video queries
export const CREATE_VIDEOS_TABLE = `
  CREATE TABLE IF NOT EXISTS game_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    file_path VARCHAR(255) NOT NULL,
    content_type VARCHAR(50) NOT NULL,
    size INT NOT NULL,
    code VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )
`;
export const INSERT_VIDEO = 'INSERT INTO game_videos(`name`, `file_path`, `content_type`, `size`, `code`) VALUES (?, ?, ?, ?, ?)';
export const SELECT_VIDEOS = 'SELECT * FROM game_videos';
export const SELECT_VIDEO_BY_NAME = 'SELECT * FROM game_videos WHERE name = ?';
export const SELECT_VIDEO_BY_CODE = 'SELECT * FROM game_videos WHERE code = ?';

// Team-Video relations
export const CREATE_TEAM_VIDEOS_TABLE = `
  CREATE TABLE IF NOT EXISTS team_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    team_name VARCHAR(255) NOT NULL,
    video_name VARCHAR(255) NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY team_video_unique (team_name, video_name)
  )
`;
export const INSERT_TEAM_VIDEO = 'INSERT INTO team_videos(`team_name`, `video_name`) VALUES (?, ?)';
export const SELECT_TEAM_VIDEOS = 'SELECT v.* FROM team_videos tv JOIN game_videos v ON tv.video_name = v.name WHERE tv.team_name = ?';
export const COUNT_TEAM_VIDEOS = 'SELECT COUNT(*) as count FROM team_videos WHERE team_name = ?';
export const COUNT_TOTAL_VIDEOS = 'SELECT COUNT(*) as count FROM game_videos';
