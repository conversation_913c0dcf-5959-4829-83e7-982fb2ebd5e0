// Skript pro testování př<PERSON><PERSON>í s platným kódem
const http = require('http');

// Funkce pro testování přihlášení
function testLogin(teamName, teamPassword, code) {
  return new Promise((resolve, reject) => {
    // Nejprve ověříme kód
    const verifyCodeData = JSON.stringify({ code });

    const verifyOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/verify-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': verifyCodeData.length
      }
    };

    console.log(`Ově<PERSON><PERSON><PERSON> kód "${code}"...`);

    const verifyReq = http.request(verifyOptions, (verifyRes) => {
      let verifyResponseData = '';

      verifyRes.on('data', (chunk) => {
        responseData += chunk;
      });

      verifyRes.on('end', () => {
        try {
          const verifyResult = JSON.parse(responseData);

          if (!verifyResult.valid) {
            resolve({
              step: 'verify-code',
              status: verifyRes.statusCode,
              result: verifyResult,
              success: false
            });
            return;
          }

          // Kód je platný, pokračujeme přihlášením
          console.log(`Kód "${code}" je platný, pokračuji přihlášením...`);

          // Pro metodu GET musíme parametry přidat do URL
          const queryParams = new URLSearchParams({
            name: teamName,
            password: teamPassword
          }).toString();

          const loginOptions = {
            hostname: 'localhost',
            port: 3000,
            path: `/api/login?${queryParams}`,
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          };

          const loginReq = http.request(loginOptions, (loginRes) => {
            let loginResponseData = '';

            loginRes.on('data', (chunk) => {
              loginResponseData += chunk;
            });

            loginRes.on('end', () => {
              try {
                const loginResult = JSON.parse(loginResponseData);

                resolve({
                  step: 'login',
                  status: loginRes.statusCode,
                  result: loginResult,
                  success: loginRes.statusCode === 200,
                  verifyResult: verifyResult
                });
              } catch (error) {
                reject({
                  step: 'login',
                  error: 'Chyba při zpracování odpovědi',
                  details: error,
                  raw: loginResponseData
                });
              }
            });
          });

          loginReq.on('error', (error) => {
            reject({
              step: 'login',
              error: 'Chyba při volání API',
              details: error
            });
          });

          loginReq.end();

        } catch (error) {
          reject({
            step: 'verify-code',
            error: 'Chyba při zpracování odpovědi',
            details: error,
            raw: verifyResponseData
          });
        }
      });
    });

    verifyReq.on('error', (error) => {
      reject({
        step: 'verify-code',
        error: 'Chyba při volání API',
        details: error
      });
    });

    verifyReq.write(verifyCodeData);
    verifyReq.end();
  });
}

// Hlavní funkce pro testování přihlášení
async function testLoginWithValidCode() {
  try {
    // Testovací údaje
    const teamName = 'testTeam';
    const teamPassword = 'testPassword';
    const validCode = 'LrFYwPr9ciOm07PB';

    // Testování přihlášení s platným kódem
    console.log(`\nTestuji přihlášení s platným kódem "${validCode}"...`);

    const result = await testLogin(teamName, teamPassword, validCode);

    console.log(`\nVýsledek testu přihlášení:`);
    console.log(`Krok: ${result.step}`);
    console.log(`Status: ${result.status}`);
    console.log(`Úspěch: ${result.success ? 'ANO ✓' : 'NE ✗'}`);

    if (result.step === 'verify-code' && !result.success) {
      console.log(`Chyba při ověření kódu: ${result.result.message}`);
    } else if (result.step === 'login') {
      console.log(`Ověření kódu: ÚSPĚŠNÉ ✓`);
      console.log(`Video: ${result.verifyResult.videoName}`);
      console.log(`Cesta k videu: ${result.verifyResult.videoPath}`);

      if (result.success) {
        console.log(`Přihlášení: ÚSPĚŠNÉ ✓`);
      } else {
        console.log(`Přihlášení: NEÚSPĚŠNÉ ✗`);
        console.log(`Chyba: ${result.result.message || 'Neznámá chyba'}`);
      }
    }

  } catch (error) {
    console.error('Chyba při testování přihlášení:', error);
  }
}

// Spuštění testů
testLoginWithValidCode();
