import { NextApiRequest, NextApiResponse } from 'next';
import videos from '../../data/videos.json';

interface Video {
  name: string;
  url: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const videoNames = videos.map(video => video.name);

    const videoDetails = videos.map(video => ({
      ...video,
      path: video.url
    }));

    return res.status(200).json({
      videos: videoNames,
      videoDetails: videoDetails
    });
  } catch (error) {
    console.error('Error fetching videos:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: String(error)
    });
  }
}
