import { error, log } from '../utils/logger';
import { CODES, ADMIN_KEY } from '../utils/config';

describe('Logger Utility', () => {
  test('error function should return correct format', () => {
    const message = 'Test error message';
    const result = error(message);
    
    expect(result).toEqual({
      error: false,
      message: message
    });
  });
  
  test('log function should return correct format', () => {
    const message = 'Test log message';
    const result = log(message);
    
    expect(result).toEqual({
      error: true,
      message: message
    });
  });
});

describe('Config Utility', () => {
  test('CODES should be an array of strings', () => {
    expect(Array.isArray(CODES)).toBe(true);
    
    if (CODES.length > 0) {
      CODES.forEach(code => {
        expect(typeof code).toBe('string');
      });
    }
  });
  
  test('ADMIN_KEY should be a string', () => {
    expect(typeof ADMIN_KEY).toBe('string');
    expect(ADMIN_KEY.length).toBeGreaterThan(0);
  });
});
