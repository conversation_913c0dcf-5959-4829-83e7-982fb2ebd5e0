import React, { memo } from 'react';
import Link from 'next/link';

interface SuccessPageProps {
  teamName: string;
}

const SuccessPage: React.FC<SuccessPageProps> = ({ teamName }) => {

  return (
    <div className="base success-page">
      <div className="terminal-header">
        <div className="terminal-title">ACCESS_GRANTED.exe</div>
        <div className="terminal-controls">
          <div className="terminal-control minimize"></div>
          <div className="terminal-control maximize"></div>
          <div className="terminal-control close"></div>
        </div>
      </div>

      <div>
        <h1 className="success-title glitch" data-text="PŘÍSTUP POVOLEN">PŘÍSTUP POVOLEN</h1>
        <p className="success-message typing">Identifikace: {teamName} | Status: Aktivní</p>

        <div className="navigation-options">
          <Link href="/videos" className="nav-option">
            <span className="nav-icon">📁</span>
            <span className="nav-text">DATABÁZE VIDEÍ</span>
          </Link>
        </div>
      </div>

      <style jsx>{`
        .navigation-options {
          margin-top: 2rem;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .nav-option {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1rem;
          background: rgba(20, 20, 20, 0.7);
          color: var(--primary);
          text-decoration: none;
          font-family: 'Fira Code', monospace;
          transition: all 0.2s ease;
        }

        .nav-option:hover {
          background: rgba(40, 40, 40, 0.7);
          transform: translateX(5px);
        }

        .nav-icon {
          font-size: 1.2rem;
        }

        .nav-text {
          font-size: 0.9rem;
        }
      `}</style>
    </div>
  );
};

export default memo(SuccessPage);
