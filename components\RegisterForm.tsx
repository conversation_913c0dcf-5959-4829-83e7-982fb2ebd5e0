import React, { memo } from 'react';
import Link from 'next/link';

interface RegisterFormProps {
  teamName: string;
  teamPassword: string;
  isLoading: boolean;
  registrationStatus: { success: boolean; message: string } | null;
  setTeamName: (name: string) => void;
  setTeamPassword: (password: string) => void;
  registerTeam: () => void;
  setShowRegister: (show: boolean) => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  teamName,
  teamPassword,
  isLoading,
  registrationStatus,
  setTeamName,
  setTeamPassword,
  registerTeam,
  setShowRegister
}) => {
  return (
    <div className="base">
      <div className="terminal-header">
        <div className="terminal-title">TEAM_REGISTRATION.exe</div>
        <div className="terminal-controls">
          <div className="terminal-control minimize"></div>
          <div className="terminal-control maximize"></div>
          <div className="terminal-control close"></div>
        </div>
      </div>

      <div className="logo glitch" data-text="REGISTRACE">REGISTRACE</div>

      <div className="form-group" style={{ width: '100%' }}>
        <input
          type="text"
          placeholder="jméno_týmu"
          value={teamName}
          onChange={(e) => {
            e.preventDefault();
            setTeamName(e.target.value);
          }}
          disabled={isLoading}
          spellCheck="false"
          autoComplete="off"
        />
      </div>

      <div className="form-group" style={{ width: '100%' }}>
        <input
          type="password"
          placeholder="heslo_týmu"
          value={teamPassword}
          onChange={(e) => {
            e.preventDefault();
            setTeamPassword(e.target.value);
          }}
          disabled={isLoading}
          spellCheck="false"
          autoComplete="off"
        />
      </div>

      {registrationStatus && (
        <div
          className={`status-message ${registrationStatus.success ? 'success' : 'error'}`}
          style={{
            color: registrationStatus.success ? 'var(--success)' : 'var(--error)',
            fontSize: '0.9rem',
            marginTop: '1rem',
            fontFamily: 'Fira Code, monospace',
            padding: '0.5rem',
            border: `1px solid ${registrationStatus.success ? 'var(--success)' : 'var(--error)'}`,
            background: registrationStatus.success ? 'rgba(0, 255, 0, 0.1)' : 'rgba(255, 0, 0, 0.1)',
          }}
        >
          {registrationStatus.message}
        </div>
      )}

      <button
        onClick={registerTeam}
        disabled={isLoading || !teamName.trim() || !teamPassword.trim()}
        className={`submit-button ${isLoading ? 'loading' : ''}`}
      >
        {isLoading ? 'REGISTRACE...' : 'REGISTROVAT TÝM'}
      </button>

      <div className="registered-teams">
        <Link
          href="/teams"
          style={{
            color: 'var(--primary-dim)',
            textDecoration: 'none',
            border: '1px solid var(--primary-dim)',
            padding: '0.5rem 1rem',
            display: 'inline-block',
            transition: 'all 0.3s ease',
            fontSize: '0.9rem',
            fontFamily: 'Fira Code, monospace'
          }}
          onMouseOver={(e: React.MouseEvent<HTMLAnchorElement>) => {
            e.currentTarget.style.color = 'var(--primary)';
            e.currentTarget.style.borderColor = 'var(--primary)';
            e.currentTarget.style.background = 'rgba(0, 255, 0, 0.1)';
          }}
          onMouseOut={(e: React.MouseEvent<HTMLAnchorElement>) => {
            e.currentTarget.style.color = 'var(--primary-dim)';
            e.currentTarget.style.borderColor = 'var(--primary-dim)';
            e.currentTarget.style.background = 'transparent';
          }}
        >
          ZOBRAZIT REGISTROVANÉ TÝMY
        </Link>
      </div>

      <div
        className="mode-switch"
        onClick={() => setShowRegister(false)}
        style={{
          marginTop: '1rem',
          color: 'var(--primary-dim)',
          cursor: 'pointer',
          fontSize: '0.8rem',
          textDecoration: 'underline'
        }}
      >
        Zpět na přihlášení
      </div>
    </div>
  );
};

export default memo(RegisterForm);
