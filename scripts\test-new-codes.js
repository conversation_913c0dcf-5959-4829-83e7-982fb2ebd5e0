// Skript pro testování nových kódů z videos.json
const http = require('http');

// Funkce pro testování ověření kódu
function testVerifyCode(code) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      code: code
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/verify-code',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    console.log(`Zkouším ověřit kód: "${code}"...`);

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            result: result
          });
        } catch (error) {
          reject({
            error: 'Chyba při zpracování odpovědi',
            details: error,
            raw: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject({
        error: 'Chyba při volání API',
        details: error
      });
    });

    req.write(data);
    req.end();
  });
}

// Testování nových kódů z videos.json
async function runTests() {
  try {
    // Test nových kódů z videos.json
    console.log('\n=== NOVÉ KÓDY Z VIDEOS.JSON ===');
    const newCodes = ['A1B2', 'C3D4', 'E5F6', 'G7H8'];
    
    for (const code of newCodes) {
      try {
        const result = await testVerifyCode(code);
        console.log(`Kód "${code}": ${result.status === 200 ? 'PLATNÝ ✓' : 'NEPLATNÝ ✗'}`);
        console.log('Výsledek:', JSON.stringify(result.result, null, 2));
        console.log('---');
      } catch (error) {
        console.error('Chyba při testování:', error);
      }
    }

    // Test neplatných kódů
    console.log('\n=== NEPLATNÉ KÓDY ===');
    const invalidCodes = ['neexistujici_kod', '123456', 'test'];
    for (const code of invalidCodes) {
      try {
        const result = await testVerifyCode(code);
        console.log(`Kód "${code}": ${result.status === 200 ? 'PLATNÝ ✓' : 'NEPLATNÝ ✗'}`);
        console.log('Výsledek:', result.result);
        console.log('---');
      } catch (error) {
        console.error('Chyba při testování:', error);
      }
    }
  } catch (error) {
    console.error('Chyba při testování:', error);
  }
}

runTests();
