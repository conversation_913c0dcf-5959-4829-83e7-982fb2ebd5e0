import { pool } from "@/utils/database";
import { error, log } from "@/utils/logger";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET' && req.method !== 'POST') {
        return res.status(400).json(error("This method isn't supported!"));
    }

    const { name, password } = req.method === 'GET' ? req.query : req.body;
    if(name == null || password == null) {
        return res.status(400).json(error("Invalid data!"));
    }

    getTeam(name, password).then((value) => {
        if (value == null) {
            return res.status(400).json(error("The team doesn't exist!"));
        }

        return res.status(200).json(value);

    }).catch((e) => {
        return res.status(400).json(error("The query has failed!"));
    })
}

async function getTeam(name: string, password: string): Promise<Team> {
    const[rows] = await pool.query('SELECT `name`, `password` FROM game_teams WHERE `name` = ? AND `password` = ?', [name, password]);

    const teams = rows as Team[];

    return teams[0];
}

interface Team {
    name: string,
    password: string
}