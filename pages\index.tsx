import Head from "next/head";
import { useState, useCallback, useEffect } from "react";
import dynamic from 'next/dynamic';
import { setAuthenticated } from '../utils/auth';
import { useRouter } from 'next/router';

// Optimalizace: Dynamický import komponent pro lepší výkon
const CustomCursor = dynamic(() => import('../components/CustomCursor'), { ssr: false });
const MatrixBackground = dynamic(() => import('../components/MatrixBackground'), { ssr: false });
const LoginForm = dynamic(() => import('../components/LoginForm'));
const RegisterForm = dynamic(() => import('../components/RegisterForm'));
const SuccessPage = dynamic(() => import('../components/SuccessPage'));

export default function Home() {
  const router = useRouter();

  // Stav aplikace
  const [err, setErr] = useState<boolean>(false);
  const [done, setDone] = useState<boolean>(false);
  const [teamName, setTeamName] = useState<string>("");
  const [teamPassword, setTeamPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [accessAttempts, setAccessAttempts] = useState<number>(0);
  const [showRegister, setShowRegister] = useState<boolean>(false);
  const [registrationStatus, setRegistrationStatus] = useState<{ success: boolean; message: string } | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>("PŘÍSTUP ODEPŘEN: Neplatné přihlašovací údaje");


  // Funkce pro reset formuláře
  const reset = useCallback(() => {
    setTeamName("");
    setTeamPassword("");
  }, []);

  // Optimalizovaná funkce pro přihlášení s useCallback
  const handleSubmit = useCallback(async () => {
    if (!teamName.trim() || !teamPassword.trim()) return;

    setIsLoading(true);

    try {
      // Zkontrolujeme, zda tým existuje a heslo je správné
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: teamName, password: teamPassword }),
      });

      const data = await response.json();

      if (response.ok) {
        // Přihlášení úspěšné
        setErr(false);
        setDone(true);
        // Nastavíme autentizační status
        setAuthenticated(true);
        // Uložíme jméno týmu do sessionStorage
        sessionStorage.setItem('teamName', teamName);
        // Přesměrujeme na stránku s videi
        router.push('/videos');
      } else {
        // Přihlášení neúspěšné
        reset();
        setErr(true);
        setErrorMessage(data.message || "PŘÍSTUP ODEPŘEN: Neplatné přihlašovací údaje");
        setAccessAttempts(prev => prev + 1);
      }
    } catch (error) {
      console.error('Chyba při přihlašování:', error);
      setErr(true);
      setErrorMessage("PŘÍSTUP ODEPŘEN: Chyba při komunikaci se serverem");
      setAccessAttempts(prev => prev + 1);
    } finally {
      setIsLoading(false);
    }
  }, [teamName, teamPassword, reset, setErr, setDone, router, setErrorMessage, setAccessAttempts, setIsLoading]);

  // Optimalizovaná funkce pro registraci s useCallback
  const registerTeam = useCallback(async () => {
    if (!teamName.trim() || !teamPassword.trim()) return;

    try {
      setIsLoading(true);
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: teamName, password: teamPassword }),
      });

      const data = await response.json();

      setRegistrationStatus({
        success: response.ok,
        message: data.message,
      });

    } catch (error) {
      console.error('Chyba při registraci týmu:', error);
      setRegistrationStatus({
        success: false,
        message: 'Došlo k chybě při komunikaci se serverem',
      });
    } finally {
      setIsLoading(false);
    }
  }, [teamName, teamPassword, setIsLoading, setRegistrationStatus]);

  // Tyto efekty byly přesunuty do samostatných komponent pro lepší výkon a udržitelnost kódu


  // Renderování obsahu stránky podle stavu
  const renderContent = useCallback(() => {
    if (done) {
      // Pokud je uživatel přihlášen, zobrazíme SuccessPage
      return <SuccessPage teamName={teamName} />;
    } else if (showRegister) {
      // Pokud je aktivní registrace, zobrazíme RegisterForm
      return (
        <RegisterForm
          teamName={teamName}
          teamPassword={teamPassword}
          isLoading={isLoading}
          registrationStatus={registrationStatus}
          setTeamName={setTeamName}
          setTeamPassword={setTeamPassword}
          registerTeam={registerTeam}
          setShowRegister={setShowRegister}
        />
      );
    } else {
      // Jinak zobrazíme LoginForm
      return (
        <LoginForm
          teamName={teamName}
          teamPassword={teamPassword}
          isLoading={isLoading}
          errorMessage={errorMessage}
          accessAttempts={accessAttempts}
          setTeamName={setTeamName}
          setTeamPassword={setTeamPassword}
          handleSubmit={handleSubmit}
          setShowRegister={setShowRegister}
        />
      );
    }
  }, [
    done, showRegister, teamName, teamPassword,
    isLoading, registrationStatus, errorMessage, accessAttempts,
    handleSubmit, registerTeam, setTeamName, setTeamPassword, setShowRegister
  ]);

  return (
    <>
      <Head>
        <title>BLEK.CZ | Secure Access</title>
        <meta name="description" content="Secure access portal" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      {/* Optimalizované komponenty */}
      <CustomCursor />
      <MatrixBackground />

      <main>
        {/* Error message */}
        {err && (
          <div className="error-message">
            {errorMessage}
          </div>
        )}

        {/* Main content */}
        {renderContent()}
      </main>
    </>
  );
}
