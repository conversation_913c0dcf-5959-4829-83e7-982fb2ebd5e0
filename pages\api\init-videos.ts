import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import { pool, CREATE_VIDEOS_TABLE, INSERT_VIDEO, SELECT_VIDEOS } from '@/utils/database';
import mime from 'mime-types';

// This endpoint initializes the videos table and migrates existing videos from the filesystem
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests for security
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    console.log('Creating videos table if it doesn\'t exist...');
    try {
      // Create the videos table if it doesn't exist
      await pool.query(CREATE_VIDEOS_TABLE);
      console.log('Videos table created or already exists');
    } catch (tableError) {
      console.error('Error creating videos table:', tableError);
      return res.status(500).json({
        message: 'Error creating videos table',
        error: String(tableError)
      });
    }

    console.log('Getting existing videos from database...');
    let existingVideos: any[] = [];
    try {
      // Get existing videos from the database
      const [rows] = await pool.query(SELECT_VIDEOS);
      existingVideos = rows as any[];
      console.log(`Found ${existingVideos.length} videos in database`);
    } catch (queryError) {
      console.error('Error querying videos:', queryError);
      console.log('Assuming no existing videos');
    }

    const existingVideoNames = existingVideos.map(video => video.name);

    // Use predefined video list instead of reading from filesystem
    // This is a workaround for Vercel's size limitations
    const videoFiles = ['1.mp4', '2.mp4', '3.mp4', '4.mp4'];

    // Base URL for videos - using a public CDN for demo purposes
    // In a production environment, you would use a more secure storage solution
    const videoBaseUrl = 'https://storage.googleapis.com/duhakralovaci-videos';

    // Count how many videos were added
    let videosAdded = 0;

    // Insert videos into database if they don't already exist
    console.log(`Processing ${videoFiles.length} video files...`);
    for (const file of videoFiles) {
      const fileInfo = path.parse(file);
      const name = fileInfo.name;
      const extension = fileInfo.ext;

      // Skip if video already exists in database
      if (existingVideoNames.includes(name)) {
        console.log(`Video "${name}" already exists in database, skipping`);
        continue;
      }

      try {
        // Get file info
        console.log(`Processing video file "${file}"...`);
        // Use estimated file size since we can't access the actual file
        const estimatedFileSize = 70 * 1024 * 1024; // Approximately 70MB
        const contentType = 'video/mp4';

        // Insert video metadata into database with external URL
        console.log(`Adding video "${name}" to database (estimated size: ${Math.round(estimatedFileSize / 1024)} KB)...`);
        await pool.query(INSERT_VIDEO, [
          name,
          `${videoBaseUrl}/${file}`,
          contentType,
          estimatedFileSize
        ]);

        videosAdded++;
        console.log(`Video "${name}" added successfully`);
      } catch (insertError) {
        console.error(`Error adding video "${name}":`, insertError);
      }
    }

    return res.status(200).json({
      message: 'Videos table initialized and videos migrated successfully',
      videosAdded
    });
  } catch (error) {
    console.error('Error initializing videos table:', error);
    return res.status(500).json({
      message: 'Error initializing videos table',
      error: String(error)
    });
  }
}
