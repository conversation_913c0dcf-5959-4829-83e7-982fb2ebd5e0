import Head from "next/head";
import { useState, useEffect, useRef } from "react";

export default function Teams() {
  const cursorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (cursorRef.current) {
        cursorRef.current.style.left = `${e.clientX}px`;
        cursorRef.current.style.top = `${e.clientY}px`;
      }
    };

    const handleMouseDown = () => {
      if (cursorRef.current) {
        cursorRef.current.classList.add('active');
      }
    };

    const handleMouseUp = () => {
      if (cursorRef.current) {
        cursorRef.current.classList.remove('active');
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  useEffect(() => {
    const matrixBg = document.querySelector('.matrix-bg');
    if (!matrixBg) return;

    matrixBg.innerHTML = '';

    const characters = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789';
    const columns = Math.floor(window.innerWidth / 20);

    for (let i = 0; i < columns; i++) {
      const column = document.createElement('div');
      column.className = 'matrix-column';
      column.style.left = `${i * 20}px`;
      column.style.animationDuration = `${Math.random() * 10 + 10}s`;

      const columnHeight = Math.floor(Math.random() * 50) + 10;
      for (let j = 0; j < columnHeight; j++) {
        const char = document.createElement('div');
        char.textContent = characters.charAt(Math.floor(Math.random() * characters.length));
        char.style.opacity = j === 0 ? '1' : `${Math.random() * 0.5 + 0.1}`;
        column.appendChild(char);
      }

      matrixBg.appendChild(column);
    }
  }, []);

  return (
    <>
      <Head>
        <title>BLEK.CZ | Panel týmu</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="cursor" ref={cursorRef}></div>

      <div className="matrix-bg"></div>

      <main>
        <div className="base teams-page">
          <div className="terminal-header">
            <div className="terminal-title">PANEL.exe</div>
            <div className="terminal-controls">
              <div className="terminal-control minimize"></div>
              <div className="terminal-control maximize"></div>
              <div className="terminal-control close"></div>
            </div>
          </div>

          <div className="logo glitch" data-text="PANEL TÝMU">PANEL TÝMU</div>


        </div>
      </main>
    </>
  );
}