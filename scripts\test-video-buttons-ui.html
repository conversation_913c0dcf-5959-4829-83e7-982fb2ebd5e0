<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Video Tlačítek</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .button {
            background: #001100;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            margin: 10px;
            cursor: pointer;
            font-family: inherit;
        }
        .button:hover {
            background: #002200;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }
        .error {
            color: #ff0000;
            border-color: #ff0000;
            background: rgba(255, 0, 0, 0.1);
        }
        .success {
            color: #00ff00;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #00ff00;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Test Video Tlačítek</h1>
        
        <div class="status" id="status">
            Připravuji test...
        </div>

        <h2>Kroky testu:</h2>
        <button class="button" onclick="setAuthentication()">1. Nastavit autentizaci</button>
        <button class="button" onclick="openVideosPage()">2. Otevřít stránku s videi</button>
        <button class="button" onclick="testAPI()">3. Testovat API</button>
        <button class="button" onclick="clearAuth()">4. Vymazat autentizaci</button>

        <h2>Výsledky:</h2>
        <div id="results"></div>

        <h2>Náhled stránky:</h2>
        <iframe id="preview" src="about:blank"></iframe>
    </div>

    <script>
        function updateStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = isError ? 'status error' : 'status success';
        }

        function addResult(message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            div.style.margin = '5px 0';
            results.appendChild(div);
        }

        function setAuthentication() {
            try {
                // Nastavíme autentizaci v sessionStorage
                sessionStorage.setItem('isAuthenticated', 'true');
                sessionStorage.setItem('teamName', 'Test');
                
                updateStatus('✓ Autentizace nastavena (isAuthenticated=true, teamName=Test)');
                addResult('Autentizace byla úspěšně nastavena');
            } catch (error) {
                updateStatus('✗ Chyba při nastavování autentizace: ' + error.message, true);
                addResult('Chyba: ' + error.message);
            }
        }

        function openVideosPage() {
            try {
                const iframe = document.getElementById('preview');
                iframe.src = 'http://localhost:3000/videos?teamName=Test';
                
                updateStatus('✓ Načítám stránku s videi...');
                addResult('Stránka s videi se načítá v iframe');
                
                // Počkáme chvíli a pak zkontrolujeme
                setTimeout(() => {
                    addResult('Stránka by se měla načíst - zkontrolujte iframe níže');
                }, 2000);
            } catch (error) {
                updateStatus('✗ Chyba při otevírání stránky: ' + error.message, true);
                addResult('Chyba: ' + error.message);
            }
        }

        async function testAPI() {
            try {
                updateStatus('🔄 Testuji API...');
                
                // Test API team-videos
                const response = await fetch('/api/team-videos?teamName=Test');
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus(`✓ API funguje - nalezeno ${data.videos.length} videí`);
                    addResult(`API test úspěšný: ${data.videos.length} videí`);
                    
                    // Zobrazíme detaily videí
                    data.videos.forEach((video, index) => {
                        addResult(`Video ${index + 1}: ${video.name} (kód: ${video.code})`);
                    });
                } else {
                    updateStatus('✗ API test selhal: ' + response.status, true);
                    addResult('API chyba: ' + response.status);
                }
            } catch (error) {
                updateStatus('✗ Chyba při testování API: ' + error.message, true);
                addResult('API chyba: ' + error.message);
            }
        }

        function clearAuth() {
            try {
                sessionStorage.removeItem('isAuthenticated');
                sessionStorage.removeItem('teamName');
                
                updateStatus('✓ Autentizace vymazána');
                addResult('Autentizace byla vymazána');
            } catch (error) {
                updateStatus('✗ Chyba při mazání autentizace: ' + error.message, true);
                addResult('Chyba: ' + error.message);
            }
        }

        // Automaticky zkontrolujeme současný stav
        window.onload = function() {
            const isAuth = sessionStorage.getItem('isAuthenticated');
            const teamName = sessionStorage.getItem('teamName');
            
            if (isAuth === 'true') {
                updateStatus(`✓ Již autentizován jako tým: ${teamName || 'neznámý'}`);
                addResult('Autentizace je již aktivní');
            } else {
                updateStatus('⚠ Není autentizován - klikněte na "Nastavit autentizaci"');
                addResult('Autentizace není nastavena');
            }
        };
    </script>
</body>
</html>
