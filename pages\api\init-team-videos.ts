import { NextApiRequest, NextApiResponse } from 'next';
import { pool, CREATE_TEAM_VIDEOS_TABLE } from '@/utils/database';

// This endpoint initializes the team_videos table
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests for security
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    console.log('Creating team_videos table if it doesn\'t exist...');
    
    // Create the team_videos table if it doesn't exist
    await pool.query(CREATE_TEAM_VIDEOS_TABLE);
    
    console.log('Team_videos table created or already exists');
    
    return res.status(200).json({ 
      message: 'Team_videos table initialized successfully'
    });
  } catch (error) {
    console.error('Error initializing team_videos table:', error);
    return res.status(500).json({ 
      message: 'Error initializing team_videos table',
      error: String(error)
    });
  }
}
