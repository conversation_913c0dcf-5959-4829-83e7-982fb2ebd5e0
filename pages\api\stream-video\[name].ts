import { NextApiRequest, NextApiResponse } from 'next';
import { pool, SELECT_VIDEO_BY_NAME } from '@/utils/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { name } = req.query;

    if (!name || typeof name !== 'string') {
      return res.status(400).json({ message: 'Video name is required' });
    }

    // Get video metadata from database
    const [rows] = await pool.query(SELECT_VIDEO_BY_NAME, [name]);
    const videos = rows as { file_path: string, content_type: string }[];

    if (videos.length === 0) {
      return res.status(404).json({ message: 'Video not found' });
    }

    const video = videos[0];

    // Sestavíme URL k videu
    // Na Vercelu jsou statické soubory dostupné přímo z kořenové cesty
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || `${req.headers['x-forwarded-proto'] || 'http'}://${req.headers.host}`;

    // Upravíme cestu k videu - odstraníme počáteční lomítko z file_path, pokud existuje
    const videoPath = video.file_path.startsWith('/') ? video.file_path.substring(1) : video.file_path;
    const videoUrl = `${baseUrl}/${videoPath}`;

    console.log(`Streaming video: ${name}, URL: ${videoUrl}`);

    return res.status(200).json({
      url: videoUrl,
      contentType: video.content_type
    });
  } catch (error) {
    console.error('Error streaming video:', error);
    return res.status(500).json({
      message: 'Error streaming video',
      error: String(error)
    });
  }
}
