import { NextApiRequest, NextApiResponse } from 'next';
import { pool, SELECT_VIDEO_BY_CODE, INSERT_TEAM_VIDEO, SELECT_TEAM_BY_NAME } from '@/utils/database';

// Interface for video data
interface Video {
  id: number;
  name: string;
  file_path: string;
  content_type: string;
  size: number;
  code: string;
  created_at: Date;
}

// Function to get video by code from database
const getVideoByCode = async (code: string): Promise<Video | null> => {
  try {
    const [rows] = await pool.query(SELECT_VIDEO_BY_CODE, [code]);
    const videos = rows as Video[];
    return videos.length > 0 ? videos[0] : null;
  } catch (error) {
    console.error('Error fetching video from database:', error);
    throw error;
  }
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { code, teamName } = req.body;

    if (!code || typeof code !== 'string' || code.trim() === '') {
      return res.status(400).json({ message: 'Přístupový kód je povinný' });
    }

    // Check if team exists
    if (teamName) {
      const [teamRows] = await pool.query(SELECT_TEAM_BY_NAME, [teamName]);
      const teams = teamRows as { name: string }[];

      if (teams.length === 0) {
        return res.status(401).json({
          valid: false,
          message: 'Tým neexistuje'
        });
      }
    }

    // Check if code matches a video code in the database
    const video = await getVideoByCode(code);

    if (video) {
      // If team name is provided, add video to team's unlocked videos
      if (teamName) {
        try {
          await pool.query(INSERT_TEAM_VIDEO, [teamName, video.name]);
          console.log(`Video "${video.name}" unlocked for team "${teamName}"`);
        } catch (error) {
          // Ignore duplicate entry errors (video already unlocked for team)
          console.log(`Video "${video.name}" already unlocked for team "${teamName}" or other error:`, error);
        }
      }

      // Code is valid, return video details
      return res.status(200).json({
        valid: true,
        videoName: video.name,
        videoPath: video.file_path, // URL z databáze
        videoCode: video.code
      });
    } else {
      // Code is invalid
      return res.status(401).json({
        valid: false,
        message: 'Neplatný přístupový kód'
      });
    }
  } catch (error) {
    console.error('Error verifying access code:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: String(error)
    });
  }
}
