import { NextApiRequest, NextApiResponse } from 'next';
import { pool, SELECT_TEAMS, COUNT_TEAM_VIDEOS, COUNT_TOTAL_VIDEOS } from '@/utils/database';
import { error } from 'console';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).send(error("This method isn't supported!"));
  }

  try {
    const teams = await getTeams();

    const sortedTeams = [...teams].sort((a, b) =>
      new Date(b.time).getTime() - new Date(a.time).getTime()
    );

    return res.status(200).json({ teams: sortedTeams });
  } catch (error) {
    console.error('The teams has failed:', error);
    return res.status(500).json({
      message: 'Internal Server Error',
      error: String(error)
    });
  }
}

async function getTeams(): Promise<Team[]> {
  // Get all teams
  const [rows] = await pool.query(SELECT_TEAMS);
  const teams = rows as Team[];

  // Get total number of videos
  const [totalVideosResult] = await pool.query(COUNT_TOTAL_VIDEOS);
  const totalVideos = (totalVideosResult as any[])[0].count;

  // If there are no videos, return teams without video stats
  if (totalVideos === 0) {
    return teams;
  }

  // Get video stats for each team
  for (const team of teams) {
    const [teamVideosResult] = await pool.query(COUNT_TEAM_VIDEOS, [team.name]);
    const unlockedVideos = (teamVideosResult as any[])[0].count;

    team.videos = {
      unlocked: unlockedVideos,
      total: totalVideos,
      percentage: Math.round((unlockedVideos / totalVideos) * 100)
    };
  }

  return teams;
}

interface Team {
  id: number,
  name: string,
  password: string,
  status: number | 0,
  time: Date,
  videos?: {
    unlocked: number,
    total: number,
    percentage: number
  }
}