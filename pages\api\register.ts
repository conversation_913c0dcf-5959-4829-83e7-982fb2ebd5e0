import { NextApiRequest, NextApiResponse } from "next";
import { error, log } from "@/utils/logger"
import { INSERT_TEAM, pool } from "@/utils/database";
 
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    res.setHeader("Content-Type", "application/json");

    if (req.method !== 'POST') {
        return res.status(405).send(error("This method isn't supported!"))
    }

    const body = req.body;

    if (body == null || body.name == null || body.password == null) {
        return res.status(400).send(error("Invalid data!"));
    }

    const name: string = body.name;
    const password: string = body.password;

    addTeam(name, password).then(() => {
        return res.status(200).send(log("Tým byl uspěšně registrován!"));
    }).catch((e) => {
        return res.status(400).send(error("The query has failed!"));
    });

}

export async function addTeam(name: string, password: string): Promise<void> {
    await pool.query(INSERT_TEAM, [name, password, 0, new Date()]);
}