// Skript pro testování API pro získávání videí
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/videos',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
};

console.log('Zkouším získat seznam videí z API...');

const req = http.request(options, (res) => {
  let data = '';

  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    try {
      const result = JSON.parse(data);
      console.log('Odpověď z API:');
      console.log(`Status: ${res.statusCode}`);
      console.log('Seznam videí:');
      if (result.videos && Array.isArray(result.videos)) {
        result.videos.forEach(video => {
          console.log(`- ${video}`);
        });
        console.log(`Celkem videí: ${result.videos.length}`);
      } else {
        console.log('Žádná videa nebyla nalezena nebo neplatný formát odpovědi');
      }

      console.log('\nDetail videí:');
      if (result.videoDetails && Array.isArray(result.videoDetails)) {
        result.videoDetails.forEach(video => {
          console.log(`- Název: ${video.name}, Cesta: ${video.path}`);
        });
      } else {
        console.log('Žádné detaily videí nebyly nalezeny nebo neplatný formát odpovědi');
      }
    } catch (error) {
      console.error('Chyba při zpracování odpovědi:', error);
      console.log('Surová odpověď:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('Chyba při volání API:', error);
});

req.end();
