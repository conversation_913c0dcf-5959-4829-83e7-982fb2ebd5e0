import React, { memo } from 'react';

interface LoginFormProps {
  teamName: string;
  teamPassword: string;
  isLoading: boolean;
  errorMessage: string;
  accessAttempts: number;
  setTeamName: (name: string) => void;
  setTeamPassword: (password: string) => void;
  handleSubmit: () => void;
  setShowRegister: (show: boolean) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  teamName,
  teamPassword,
  isLoading,
  errorMessage,
  accessAttempts,
  setTeamName,
  setTeamPassword,
  handleSubmit,
  setShowRegister
}) => {
  return (
    <div className="base">
      <div className="terminal-header">
        <div className="terminal-title">SECURE_LOGIN.exe</div>
        <div className="terminal-controls">
          <div className="terminal-control minimize"></div>
          <div className="terminal-control maximize"></div>
          <div className="terminal-control close"></div>
        </div>
      </div>

      <div className="logo glitch" data-text="BLEK.CZ">BLEK.CZ</div>

      <div className="form-group" style={{ width: '100%' }}>
        <label htmlFor="team-name" className="sr-only">Identifikace týmu</label>
        <input
          id="team-name"
          type="text"
          placeholder="identifikace_týmu"
          value={teamName}
          onChange={(e) => {
            e.preventDefault();
            setTeamName(e.target.value);
          }}
          disabled={isLoading}
          spellCheck="false"
          autoComplete="off"
          aria-required="true"
          aria-label="Identifikace týmu"
        />
      </div>

      <div className="form-group" style={{ width: '100%' }}>
        <label htmlFor="team-password" className="sr-only">Heslo týmu</label>
        <input
          id="team-password"
          type="password"
          placeholder="heslo_týmu"
          value={teamPassword}
          onChange={(e) => {
            e.preventDefault();
            setTeamPassword(e.target.value);
          }}
          disabled={isLoading}
          spellCheck="false"
          autoComplete="off"
          aria-required="true"
          aria-label="Heslo týmu"
        />
      </div>

      {/* Pole pro přístupový kód bylo odstraněno */}

      <button
        onClick={handleSubmit}
        disabled={isLoading || !teamName.trim() || !teamPassword.trim()}
        className={`submit-button ${isLoading ? 'loading' : ''}`}
        aria-busy={isLoading}
        aria-live="polite"
      >
        {isLoading ? 'OVĚŘOVÁNÍ...' : 'PŘIHLÁSIT SE'}
      </button>

      {accessAttempts > 0 && (
        <div style={{ color: 'var(--error)', fontSize: '0.8rem', marginTop: '1rem', fontFamily: 'Fira Code, monospace' }}>
          VAROVÁNÍ: Neúspěšné pokusy o přístup: {accessAttempts}
        </div>
      )}

      <div
        className="mode-switch"
        onClick={() => setShowRegister(true)}
        style={{
          marginTop: '1rem',
          color: 'var(--primary-dim)',
          cursor: 'pointer',
          fontSize: '0.8rem',
          textDecoration: 'underline'
        }}
      >
        Registrovat nový tým
      </div>
    </div>
  );
};

export default memo(LoginForm);
