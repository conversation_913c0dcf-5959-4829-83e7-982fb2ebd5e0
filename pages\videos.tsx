import Head from "next/head";
import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/router";
import dynamic from 'next/dynamic';
import Link from "next/link";
import { isAuthenticated } from "../utils/auth";

// Dynamický import komponent pro lepší výkon
const CustomCursor = dynamic(() => import('../components/CustomCursor'), { ssr: false });
const MatrixBackground = dynamic(() => import('../components/MatrixBackground'), { ssr: false });

interface Video {
  name: string;
  path: string;
}

export default function Videos() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [code, setCode] = useState<string>("");
  const [codeError, setCodeError] = useState<string | null>(null);
  const [codeSuccess, setCodeSuccess] = useState<string | null>(null);
  const [teamName, setTeamName] = useState<string>("");
  const [stats, setStats] = useState<{ unlocked: number; total: number; percentage: number } | null>(null);
  const router = useRouter();

  // Fetch team videos from API
  const fetchTeamVideos = useCallback(async (team: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/team-videos?teamName=${encodeURIComponent(team)}`);

      if (!response.ok) {
        throw new Error('Chyba při načítání videí');
      }

      const data = await response.json();

      setVideos(data.videos || []);
      setStats(data.stats || null);
      setError(null);
    } catch (error) {
      console.error('Chyba při načítání videí:', error);
      setError('Nepodařilo se načíst seznam videí');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle code submission
  const handleCodeSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!code.trim()) {
      setCodeError('Zadejte přístupový kód');
      return;
    }

    try {
      setIsLoading(true);
      setCodeError(null);
      setCodeSuccess(null);

      const response = await fetch('/api/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code, teamName }),
      });

      const data = await response.json();

      if (response.ok && data.valid) {
        setCodeSuccess(`Video "${data.videoName}" bylo odemknuto!`);
        setCode("");
        // Refresh the list of videos
        fetchTeamVideos(teamName);
      } else {
        setCodeError(data.message || 'Neplatný přístupový kód');
      }
    } catch (error) {
      console.error('Chyba při ověřování kódu:', error);
      setCodeError('Chyba při ověřování kódu');
    } finally {
      setIsLoading(false);
    }
  }, [code, teamName, fetchTeamVideos]);

  // Check authentication on component mount
  useEffect(() => {
    // Get team name from URL parameter or sessionStorage
    const urlTeamName = router.query.teamName as string;
    const storedTeamName = sessionStorage.getItem('teamName');

    const teamToUse = urlTeamName || storedTeamName;

    // If we have a team name from URL, allow access for testing
    if (teamToUse) {
      setTeamName(teamToUse);
      // Store in sessionStorage for future use
      if (urlTeamName) {
        sessionStorage.setItem('teamName', urlTeamName);
        sessionStorage.setItem('isAuthenticated', 'true'); // Auto-authenticate for testing
      }
      fetchTeamVideos(teamToUse);
    } else if (!isAuthenticated()) {
      // Only redirect if not authenticated AND no team name provided
      router.push('/');
    } else {
      setError('Nebylo možné načíst informace o týmu');
    }
  }, [fetchTeamVideos, router, router.query.teamName]);


  // Handle video selection
  const handleVideoSelect = (video: Video) => {
    setSelectedVideo(video);
  };

  // Close video player and ensure session is preserved
  const closeVideoPlayer = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedVideo(null);
  };

  return (
    <>
      <Head>
        <title>BLEK.CZ | Videa</title>
        <meta name="description" content="Seznam dostupných videí" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      {/* Custom cursor and Matrix background */}
      <CustomCursor />
      <MatrixBackground />

      {/* Video player modal */}
      {selectedVideo && (
        <div className="video-container">
          <div className="video-player-header">
            <button className="back-btn" onClick={closeVideoPlayer}>
              &larr; Zpět na seznam videí
            </button>
            <div className="video-title">{selectedVideo.name}</div>
          </div>
          <div className="video-player-wrapper">
            <iframe
              className="fullscreen-video"
              src={selectedVideo.path}
              title="YouTube video player"
              style={{ border: 0 }}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        </div>
      )}

      <main>
        <div className="base videos-page">
          <div className="terminal-header">
            <div className="terminal-title">VIDEOS_DATABASE.exe</div>
            <div className="terminal-controls">
              <div className="terminal-control minimize"></div>
              <div className="terminal-control maximize"></div>
              <div className="terminal-control close"></div>
            </div>
          </div>

          <div className="logo glitch" data-text="DATABÁZE VIDEÍ">DATABÁZE VIDEÍ</div>

          <div className="navigation-links">
            <Link href="/" className="nav-link">
              &lt; ZPĚT NA HLAVNÍ STRÁNKU
            </Link>
          </div>

          {teamName && (
            <div className="team-info">
              <div className="team-name-container">
                Tým: <span className="team-name">{teamName}</span>
              </div>
              {stats && (
                <div className="team-stats">
                  <div className="stats-progress">
                    <div
                      className="progress-bar"
                      style={{ width: `${stats.percentage}%` }}
                      title={`${stats.percentage}% dokončeno`}
                    ></div>
                  </div>
                  <div className="stats-text">
                    Odemknuto videí: <span className="stats-value">{stats.unlocked}</span> z <span className="stats-value">{stats.total}</span> ({stats.percentage}%)
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Formulář pro zadání kódu */}
          <div className="code-form-container">
            <form onSubmit={handleCodeSubmit} className="code-form">
              <div className="form-group">
                <input
                  type="text"
                  placeholder="Zadejte přístupový kód"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  disabled={isLoading}
                  className="code-input"
                />
                <button
                  type="submit"
                  disabled={isLoading || !code.trim()}
                  className="code-submit-btn"
                >
                  {isLoading ? 'Ověřování...' : 'Ověřit kód'}
                </button>
              </div>
              {codeError && <div className="code-error">{codeError}</div>}
              {codeSuccess && <div className="code-success">{codeSuccess}</div>}
            </form>
          </div>

          <div className="videos-section">
            <h2 className="section-title">Odemknutá videa</h2>
            {isLoading ? (
              <div className="loading-message">Načítání seznamu videí...</div>
            ) : error ? (
              <div className="error-message">{error}</div>
            ) : videos.length > 0 ? (
              <div className="videos-list-container">
                <div className="videos-list">
                  {videos.map((video) => (
                    <button
                      key={video.name}
                      className="video-button"
                      onClick={() => handleVideoSelect(video)}
                      type="button"
                    >
                      <div className="video-button-content">
                        <div className="video-icon">🎬</div>
                        <div className="video-details">
                          <div className="video-name">{video.name}</div>
                          <div className="video-subtitle">Klikněte pro přehrání</div>
                        </div>
                        <div className="video-play-icon">▶</div>
                      </div>
                    </button>
                  ))}
                </div>
                <div className="videos-count">
                  Celkem videí: <span className="count">{videos.length}</span>
                </div>
              </div>
            ) : (
              <div className="no-videos-message">Žádná videa nejsou k dispozici</div>
            )}
          </div>
        </div>
      </main>

      <style jsx>{`
        .videos-page {
          max-width: 800px;
        }

        .team-info {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          margin: 1rem 0;
          font-size: 0.9rem;
          background: rgba(20, 20, 20, 0.7);
          padding: 1rem;
          border-left: 3px solid var(--primary);
        }

        .team-name-container {
          margin-bottom: 0.5rem;
        }

        .team-name {
          color: var(--primary);
          font-weight: bold;
        }

        .team-stats {
          margin-top: 0.75rem;
        }

        .stats-progress {
          width: 100%;
          height: 8px;
          background: rgba(0, 0, 0, 0.3);
          margin-bottom: 0.5rem;
          overflow: hidden;
        }

        .progress-bar {
          height: 100%;
          background: var(--primary);
          transition: width 0.3s ease;
        }

        .stats-text {
          font-size: 0.8rem;
        }

        .stats-value {
          color: var(--primary);
          font-weight: bold;
        }

        .code-form-container {
          margin: 2rem 0;
          padding: 1rem;
          background: rgba(20, 20, 20, 0.7);
          border-left: 3px solid var(--primary);
        }

        .code-form {
          width: 100%;
        }

        .form-group {
          display: flex;
          gap: 0.5rem;
        }

        .code-input {
          flex: 1;
          background: rgba(0, 0, 0, 0.3);
          border: 1px solid var(--primary-dim);
          color: var(--primary);
          padding: 0.75rem;
          font-family: 'Fira Code', monospace;
          outline: none;
        }

        .code-input:focus {
          border-color: var(--primary);
        }

        .code-submit-btn {
          background: rgba(0, 0, 0, 0.5);
          border: 1px solid var(--primary);
          color: var(--primary);
          padding: 0.5rem 1rem;
          cursor: pointer;
          font-family: 'Fira Code', monospace;
          transition: all 0.2s ease;
        }

        .code-submit-btn:hover:not(:disabled) {
          background: rgba(var(--primary-rgb), 0.2);
        }

        .code-submit-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .code-error {
          color: var(--error);
          margin-top: 0.5rem;
          font-family: 'Fira Code', monospace;
          font-size: 0.9rem;
        }

        .code-success {
          color: var(--success);
          margin-top: 0.5rem;
          font-family: 'Fira Code', monospace;
          font-size: 0.9rem;
        }

        .section-title {
          font-family: 'Fira Code', monospace;
          color: var(--primary);
          margin: 2rem 0 1rem;
          font-size: 1.2rem;
          border-bottom: 1px solid var(--primary-dim);
          padding-bottom: 0.5rem;
        }

        .videos-list-container {
          width: 100%;
          margin-top: 1rem;
        }

        .videos-list {
          width: 100%;
          max-height: 500px;
          overflow-y: auto;
          background: rgba(0, 0, 0, 0.3);
          padding: 0.5rem;
          margin-bottom: 1rem;
        }

        .video-button {
          width: 100%;
          background: linear-gradient(135deg, rgba(20, 20, 20, 0.9), rgba(40, 40, 40, 0.7));
          border: 2px solid var(--primary-dim);
          border-radius: 8px;
          padding: 0;
          margin-bottom: 1rem;
          cursor: pointer;
          transition: all 0.3s ease;
          font-family: inherit;
          overflow: hidden;
          position: relative;
        }

        .video-button:hover {
          border-color: var(--primary);
          background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(60, 60, 60, 0.7));
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 255, 0, 0.2);
        }

        .video-button:active {
          transform: translateY(0);
          box-shadow: 0 4px 15px rgba(0, 255, 0, 0.3);
        }

        .video-button-content {
          display: flex;
          align-items: center;
          padding: 1rem;
          gap: 1rem;
        }

        .video-icon {
          font-size: 2rem;
          filter: grayscale(1);
          transition: filter 0.3s ease;
        }

        .video-button:hover .video-icon {
          filter: grayscale(0);
        }

        .video-details {
          flex: 1;
          text-align: left;
        }

        .video-name {
          font-family: 'Fira Code', monospace;
          color: var(--primary);
          font-size: 1.1rem;
          font-weight: bold;
          margin-bottom: 0.25rem;
        }

        .video-subtitle {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          font-size: 0.85rem;
          opacity: 0.8;
        }

        .video-play-icon {
          color: var(--primary);
          font-size: 1.5rem;
          transition: transform 0.3s ease;
        }

        .video-button:hover .video-play-icon {
          transform: scale(1.2);
          color: #00ff00;
        }

        .videos-count {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          font-size: 0.9rem;
        }

        .count {
          color: var(--primary);
          font-weight: bold;
        }

        .loading-message, .no-videos-message, .error-message {
          font-family: 'Fira Code', monospace;
          color: var(--primary-dim);
          margin: 2rem 0;
          text-align: center;
        }

        .error-message {
          color: var(--error);
        }

        .navigation-links {
          margin: 1rem 0;
          display: flex;
          justify-content: flex-start;
        }

        .nav-link {
          color: var(--primary-dim);
          font-family: 'Fira Code', monospace;
          font-size: 0.9rem;
          text-decoration: none;
          transition: color 0.2s ease;
        }

        .nav-link:hover {
          color: var(--primary);
        }

        .video-container {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          display: flex;
          flex-direction: column;
          z-index: 999;
          background: #000;
        }

        .video-player-header {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding: 0.5rem 1rem;
          background: rgba(20, 20, 20, 0.7);
          z-index: 10;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          opacity: 0.8;
          transition: opacity 0.3s ease;
        }

        .video-player-header:hover {
          opacity: 1;
        }

        .video-title {
          font-family: 'Fira Code', monospace;
          color: var(--primary);
          margin-left: 1rem;
        }

        .back-btn {
          background: rgba(0, 0, 0, 0.5);
          border: 1px solid var(--primary);
          color: var(--primary);
          padding: 0.5rem 1rem;
          font-family: 'Fira Code', monospace;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.9rem;
        }

        .back-btn:hover {
          background: rgba(var(--primary-rgb), 0.2);
        }

        .fullscreen-video {
          width: 100%;
          height: 100%;
          object-fit: contain;
          padding-top: 40px; /* Prostor pro hlavičku */
        }

        .video-loading, .video-error {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: calc(100% - 40px);
          font-family: 'Fira Code', monospace;
          color: var(--primary);
          font-size: 1.2rem;
          text-align: center;
          padding: 2rem;
        }

        .video-error {
          color: var(--error);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          text-align: center;
          height: calc(100% - 40px);
          overflow-y: auto;
        }

        .video-error h3 {
          margin-bottom: 1rem;
          font-size: 1.5rem;
        }

        .video-error p {
          margin-bottom: 1.5rem;
          max-width: 600px;
        }

        .video-debug-info {
          background: rgba(0, 0, 0, 0.7);
          padding: 1rem;
          border: 1px solid var(--error);
          border-radius: 4px;
          margin: 1rem 0;
          text-align: left;
          max-width: 600px;
          overflow: auto;
        }

        .video-debug-info h4 {
          margin-bottom: 0.5rem;
          color: var(--primary);
        }

        .video-debug-info pre {
          white-space: pre-wrap;
          word-break: break-all;
          font-size: 0.8rem;
          color: var(--primary-dim);
        }

        .video-player-wrapper {
          position: relative;
          width: 100%;
          height: 100%;
        }

        .video-controls {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.7);
          padding: 1rem;
          text-align: center;
          z-index: 10;
        }

        .video-controls p {
          color: var(--primary);
          font-size: 0.9rem;
        }

        .video-controls a {
          color: var(--primary);
          text-decoration: underline;
        }

        .video-controls a:hover {
          color: white;
        }
      `}</style>
    </>
  );
}
