import { NextApiRequest, NextApiResponse } from 'next';
import { pool, SELECT_TEAM_VIDEOS, SELECT_TEAM_BY_NAME } from '@/utils/database';
import { isAuthenticated } from '@/utils/auth';

// Interface for video data
interface Video {
  id: number;
  name: string;
  file_path: string;
  content_type: string;
  size: number;
  created_at: Date;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Get team name from query parameters
    const { teamName } = req.query;

    if (!teamName || typeof teamName !== 'string') {
      return res.status(400).json({ message: 'Team name is required' });
    }

    // Check if team exists
    const [teamRows] = await pool.query(SELECT_TEAM_BY_NAME, [teamName]);
    const teams = teamRows as { name: string }[];

    if (teams.length === 0) {
      return res.status(404).json({ message: 'Team not found' });
    }

    // Get videos unlocked by the team
    const [rows] = await pool.query(SELECT_TEAM_VIDEOS, [teamName]);
    const videos = rows as Video[];

    // Get total number of videos
    const [allVideosRows] = await pool.query('SELECT COUNT(*) as total FROM game_videos');
    const totalVideos = (allVideosRows as any[])[0].total;

    // Format the response
    const formattedVideos = videos.map(video => ({
      ...video,
      path: `/api/stream-video/${video.name}`
    }));

    return res.status(200).json({
      teamName,
      videos: formattedVideos,
      stats: {
        unlocked: videos.length,
        total: totalVideos,
        percentage: Math.round((videos.length / totalVideos) * 100)
      }
    });
  } catch (error) {
    console.error('Error fetching team videos:', error);
    return res.status(500).json({
      message: 'Error fetching team videos',
      error: String(error)
    });
  }
}
