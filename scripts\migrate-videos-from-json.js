// Skript pro migraci videí z videos.json do databáze
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Konfigurace databáze
const dbConfig = {
  host: 'camelot.vagonbrei.eu',
  user: 'u6813_cXlIlMkZTS',
  password: '<EMAIL>',
  database: 's6813_web-st',
  connectTimeout: 5000,
};

// Funkce pro načtení videos.json
function loadVideosFromJson() {
  try {
    const videosPath = path.join(__dirname, '..', 'data', 'videos.json');
    const videosData = fs.readFileSync(videosPath, 'utf8');
    return JSON.parse(videosData);
  } catch (error) {
    console.error('Chyba při načítání videos.json:', error.message);
    throw error;
  }
}

// Funkce pro přidání sloupce code do tabulky (pokud neexistuje)
async function addCodeColumnIfNotExists(connection) {
  try {
    console.log('<PERSON><PERSON><PERSON><PERSON>ji, zda existuje sloupec "code" v tabulce game_videos...');

    // Zkontrolujeme strukturu tabulky
    const [columns] = await connection.query('DESCRIBE game_videos');
    const hasCodeColumn = columns.some(column => column.Field === 'code');

    if (!hasCodeColumn) {
      console.log('Přidávám sloupec "code" do tabulky game_videos...');
      // Nejprve přidáme sloupec bez UNIQUE omezení
      await connection.query('ALTER TABLE game_videos ADD COLUMN code VARCHAR(255)');
      console.log('Sloupec "code" byl úspěšně přidán ✓');
    } else {
      console.log('Sloupec "code" již existuje ✓');
    }
  } catch (error) {
    console.error('Chyba při přidávání sloupce "code":', error.message);
    throw error;
  }
}

// Funkce pro vymazání současných videí
async function clearCurrentVideos(connection) {
  try {
    console.log('Mažu současná videa z databáze...');

    // Nejprve smažeme vztahy týmů k videím
    await connection.query('DELETE FROM team_videos');
    console.log('Vztahy týmů k videím byly smazány');

    // Pak smažeme videa
    await connection.query('DELETE FROM game_videos');
    console.log('Současná videa byla smazána');
  } catch (error) {
    console.error('Chyba při mazání současných videí:', error.message);
    throw error;
  }
}

// Funkce pro přidání UNIQUE omezení na sloupec code
async function addUniqueConstraintToCode(connection) {
  try {
    console.log('Přidávám UNIQUE omezení na sloupec "code"...');

    // Zkontrolujeme, zda už UNIQUE omezení existuje
    const [indexes] = await connection.query('SHOW INDEX FROM game_videos WHERE Column_name = "code"');
    const hasUniqueIndex = indexes.some(index => index.Non_unique === 0);

    if (!hasUniqueIndex) {
      await connection.query('ALTER TABLE game_videos ADD UNIQUE KEY unique_code (code)');
      console.log('UNIQUE omezení na sloupec "code" bylo přidáno ✓');
    } else {
      console.log('UNIQUE omezení na sloupec "code" již existuje ✓');
    }
  } catch (error) {
    console.error('Chyba při přidávání UNIQUE omezení:', error.message);
    throw error;
  }
}

// Funkce pro vložení videí z JSON
async function insertVideosFromJson(connection, videos) {
  try {
    console.log('Vkládám videa z videos.json...');

    for (const video of videos) {
      try {
        console.log(`Vkládám video: ${video.name} (kód: ${video.code})`);

        // Vložíme video do databáze
        await connection.query(
          'INSERT INTO game_videos (name, file_path, content_type, size, code) VALUES (?, ?, ?, ?, ?)',
          [
            video.name,
            video.url, // URL jako file_path
            'video/youtube', // content_type pro YouTube videa
            0, // size - pro YouTube videa není relevantní
            video.code
          ]
        );

        console.log(`Video "${video.name}" bylo úspěšně vloženo ✓`);
      } catch (error) {
        console.error(`Chyba při vkládání videa "${video.name}":`, error.message);
      }
    }
  } catch (error) {
    console.error('Chyba při vkládání videí:', error.message);
    throw error;
  }
}

// Funkce pro zobrazení výsledků
async function showResults(connection) {
  try {
    console.log('\n=== Výsledky migrace ===');

    const [videos] = await connection.query('SELECT * FROM game_videos');
    console.log(`Celkem videí v databázi: ${videos.length}`);

    console.log('\nSeznam videí:');
    videos.forEach((video, index) => {
      console.log(`  ${index + 1}. ${video.name} (kód: ${video.code}) - ${video.file_path}`);
    });
  } catch (error) {
    console.error('Chyba při zobrazování výsledků:', error.message);
  }
}

// Hlavní funkce migrace
async function migrateVideos() {
  let connection;

  try {
    console.log('=== Migrace videí z videos.json ===\n');

    // Načteme data z videos.json
    console.log('Načítám data z videos.json...');
    const videos = loadVideosFromJson();
    console.log(`Načteno ${videos.length} videí z videos.json ✓`);

    // Připojíme se k databázi
    console.log('\nPřipojuji se k databázi...');
    connection = await mysql.createConnection(dbConfig);
    console.log('Připojení k databázi úspěšné ✓');

    // Přidáme sloupec code pokud neexistuje
    await addCodeColumnIfNotExists(connection);

    // Smažeme současná videa
    await clearCurrentVideos(connection);

    // Vložíme videa z JSON
    await insertVideosFromJson(connection, videos);

    // Přidáme UNIQUE omezení na sloupec code
    await addUniqueConstraintToCode(connection);

    // Zobrazíme výsledky
    await showResults(connection);

    console.log('\n=== Migrace byla úspěšně dokončena ✓ ===');

  } catch (error) {
    console.error('\nChyba při migraci:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\nPřipojení k databázi ukončeno');
    }
  }
}

// Spuštění migrace
if (require.main === module) {
  migrateVideos();
}

module.exports = { migrateVideos, loadVideosFromJson };
