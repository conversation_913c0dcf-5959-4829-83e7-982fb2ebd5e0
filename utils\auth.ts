// Simple client-side authentication check
// This is a basic implementation and could be enhanced with proper session management

// Check if user is authenticated (has successfully logged in)
export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') {
    return false; // Always return false on server-side
  }
  
  // Check if authentication flag exists in session storage
  return sessionStorage.getItem('isAuthenticated') === 'true';
};

// Set authentication status
export const setAuthenticated = (status: boolean): void => {
  if (typeof window === 'undefined') {
    return; // Do nothing on server-side
  }
  
  if (status) {
    sessionStorage.setItem('isAuthenticated', 'true');
  } else {
    sessionStorage.removeItem('isAuthenticated');
  }
};

// Log out user
export const logout = (): void => {
  if (typeof window === 'undefined') {
    return; // Do nothing on server-side
  }
  
  sessionStorage.removeItem('isAuthenticated');
};
